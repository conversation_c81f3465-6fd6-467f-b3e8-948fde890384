"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/network-tree/route";
exports.ids = ["app/api/admin/network-tree/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fnetwork-tree%2Froute&page=%2Fapi%2Fadmin%2Fnetwork-tree%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fnetwork-tree%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fnetwork-tree%2Froute&page=%2Fapi%2Fadmin%2Fnetwork-tree%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fnetwork-tree%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_okdoi_src_app_api_admin_network_tree_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/network-tree/route.ts */ \"(rsc)/./src/app/api/admin/network-tree/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/network-tree/route\",\n        pathname: \"/api/admin/network-tree\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/network-tree/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\api\\\\admin\\\\network-tree\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_okdoi_src_app_api_admin_network_tree_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/network-tree/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fnetwork-tree%2Froute&page=%2Fapi%2Fadmin%2Fnetwork-tree%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fnetwork-tree%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/network-tree/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/admin/network-tree/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/referralSystem */ \"(rsc)/./src/lib/services/referralSystem.ts\");\n\n\n\n\n\nasync function GET(request) {\n    try {\n        // Create Supabase client for auth using SSR\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const supabaseAuth = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_1__.createServerClient)(\"https://vnmydqbwjjufnxngpnqo.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\", {\n            cookies: {\n                get (name) {\n                    return cookieStore.get(name)?.value;\n                }\n            }\n        });\n        // Get the current user\n        const { data: { user }, error: authError } = await supabaseAuth.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is admin using admin client\n        const { data: userData, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.from(\"users\").select(\"role, is_super_admin\").eq(\"id\", user.id).single();\n        if (userError || !userData || userData.role !== \"admin\" && !userData.is_super_admin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden - Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        // Get query parameters\n        const { searchParams } = new URL(request.url);\n        const rootUserId = searchParams.get(\"rootUserId\");\n        const maxDepth = parseInt(searchParams.get(\"maxDepth\") || \"999\") // Allow unlimited depth by default\n        ;\n        const lazyLoad = searchParams.get(\"lazy\") === \"true\";\n        let networkTree;\n        if (rootUserId) {\n            // Get network tree for specific user\n            networkTree = await getNetworkTreeForUser(rootUserId, maxDepth);\n        } else {\n            // Get complete network tree starting from OKDOI Head\n            const okdoiHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_4__.ReferralSystemService.getOKDOIHead();\n            if (!okdoiHead) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: null,\n                    message: \"No OKDOI Head found\"\n                });\n            }\n            networkTree = await getNetworkTreeForUser(okdoiHead.id, maxDepth);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: networkTree\n        });\n    } catch (error) {\n        console.error(\"Error fetching network tree:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch network tree\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Enhanced caching with TTL\nconst treeCache = new Map();\nconst CACHE_TTL = 1 * 60 * 1000 // 1 minute for testing, will increase later\n;\n// Clear cache on startup to ensure fresh data\ntreeCache.clear();\nasync function getNetworkTreeForUser(userId, maxDepth = 999) {\n    try {\n        // Check cache first\n        const cacheKey = `${userId}-${maxDepth}`;\n        const cached = treeCache.get(cacheKey);\n        if (cached && Date.now() - cached.timestamp < CACHE_TTL) {\n            return cached.data;\n        }\n        // Fetch all data in parallel for better performance\n        const [rootUserResult, allUsersResult, allPlacementsResult, allSubscriptionsResult, introducersResult] = await Promise.all([\n            // Get root user\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.from(\"users\").select(\"*\").eq(\"id\", userId).single(),\n            // Get all users in the network (with depth limit)\n            getAllNetworkUsers(userId, maxDepth),\n            // Get all placements in the network\n            getAllNetworkPlacements(userId, maxDepth),\n            // Get all active subscriptions\n            getAllActiveSubscriptions(),\n            // Get all introducers (referred_by users) for the network\n            getAllIntroducers(userId, maxDepth)\n        ]);\n        if (rootUserResult.error || !rootUserResult.data) {\n            throw new Error(\"Root user not found\");\n        }\n        const rootUser = rootUserResult.data;\n        const allUsers = allUsersResult;\n        const allPlacements = allPlacementsResult;\n        const allSubscriptions = allSubscriptionsResult;\n        const allIntroducers = introducersResult;\n        // Build lookup maps for O(1) access\n        const userMap = new Map(allUsers.map((user)=>[\n                user.id,\n                user\n            ]));\n        const introducerMap = new Map(allIntroducers.map((user)=>[\n                user.id,\n                user\n            ]));\n        const placementsByParent = new Map();\n        const subscriptionsByUser = new Map();\n        // Group placements by parent\n        allPlacements.forEach((placement)=>{\n            if (!placementsByParent.has(placement.parent_id)) {\n                placementsByParent.set(placement.parent_id, []);\n            }\n            placementsByParent.get(placement.parent_id).push(placement);\n        });\n        // Group subscriptions by user\n        allSubscriptions.forEach((sub)=>{\n            if (!subscriptionsByUser.has(sub.user_id)) {\n                subscriptionsByUser.set(sub.user_id, []);\n            }\n            subscriptionsByUser.get(sub.user_id).push(sub);\n        });\n        // Build tree using optimized non-recursive approach\n        const buildOptimizedTree = (userId, currentDepth = 0)=>{\n            const user = userMap.get(userId);\n            if (!user) return null;\n            const children = [];\n            const placements = placementsByParent.get(userId) || [];\n            if (maxDepth >= 999 || currentDepth < maxDepth) {\n                for (const placement of placements){\n                    const childNode = buildOptimizedTree(placement.child_id, currentDepth + 1);\n                    if (childNode) {\n                        childNode.position = placement.pos || placement.position || 0;\n                        childNode.placement_type = placement.placement_type;\n                        children.push(childNode);\n                    }\n                }\n            } else if (placements.length > 0) {\n                // Even if we've reached maxDepth, add placeholder children to show expand button\n                for (const placement of placements){\n                    const childUser = userMap.get(placement.child_id);\n                    if (childUser) {\n                        children.push({\n                            user: childUser,\n                            children: [],\n                            level: currentDepth + 1,\n                            position: placement.pos || placement.position || 0,\n                            placement_type: placement.placement_type,\n                            hasMoreChildren: true // Flag to indicate there are more children to load\n                        });\n                    }\n                }\n            }\n            // Attach subscription data\n            user.active_subscription = subscriptionsByUser.get(userId) || [];\n            // Attach introducer information\n            if (user.referred_by_id) {\n                const introducer = introducerMap.get(user.referred_by_id);\n                user.introducer_name = introducer ? introducer.full_name || introducer.email : \"Unknown\";\n            } else {\n                user.introducer_name = null;\n            }\n            // Calculate stats efficiently (pre-computed) and attach to user object\n            const stats = calculateUserStats(userId, allUsers, allPlacements);\n            // Attach stats directly to user object for compatibility with UI\n            user.direct_referrals_count = stats.directReferrals;\n            user.total_downline_count = stats.totalDownline;\n            user.total_commission_earned = stats.totalCommission;\n            return {\n                user,\n                children,\n                level: currentDepth,\n                position: 0,\n                stats\n            };\n        };\n        const result = buildOptimizedTree(userId, 0);\n        // Cache the final result only\n        treeCache.set(cacheKey, {\n            data: result,\n            timestamp: Date.now()\n        });\n        return result;\n    } catch (error) {\n        console.error(\"Error building network tree:\", error);\n        throw error;\n    }\n}\n// Optimized helper functions\nasync function getAllNetworkUsers(rootUserId, maxDepth) {\n    // Use recursive CTE to get all users in the network efficiently\n    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.rpc(\"get_network_users\", {\n        root_user_id: rootUserId,\n        max_depth: maxDepth\n    });\n    if (error) {\n        console.error(\"Error getting network users with RPC, using fallback:\", error);\n        // Fallback to recursive query approach\n        return await getAllNetworkUsersFallback(rootUserId, maxDepth);\n    }\n    return data || [];\n}\nasync function getAllNetworkUsersFallback(rootUserId, maxDepth, currentDepth = 0) {\n    if (currentDepth >= maxDepth && maxDepth < 999) return [] // Only limit if maxDepth is explicitly set to a reasonable number\n    ;\n    // Get root user\n    const { data: rootUser } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.from(\"users\").select(\"*\").eq(\"id\", rootUserId).single();\n    if (!rootUser) return [];\n    let allUsers = [\n        rootUser\n    ];\n    // Get children through referral_placements\n    const { data: placements } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.from(\"referral_placements\").select(\"child_id\").eq(\"parent_id\", rootUserId);\n    if (placements && placements.length > 0) {\n        for (const placement of placements){\n            const childUsers = await getAllNetworkUsersFallback(placement.child_id, maxDepth, currentDepth + 1);\n            allUsers = allUsers.concat(childUsers);\n        }\n    }\n    return allUsers;\n}\nasync function getAllNetworkPlacements(rootUserId, maxDepth) {\n    // Get all placements for the network\n    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.rpc(\"get_network_placements\", {\n        root_user_id: rootUserId,\n        max_depth: maxDepth\n    });\n    if (error) {\n        console.error(\"Error getting network placements with RPC, using fallback:\", error);\n        // Fallback to recursive query\n        return await getAllNetworkPlacementsFallback(rootUserId, maxDepth);\n    }\n    return data || [];\n}\nasync function getAllNetworkPlacementsFallback(rootUserId, maxDepth, currentDepth = 0) {\n    if (currentDepth >= maxDepth && maxDepth < 999) return [] // Only limit if maxDepth is explicitly set to a reasonable number\n    ;\n    // Get direct placements\n    const { data: placements } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.from(\"referral_placements\").select(\"*\").eq(\"parent_id\", rootUserId);\n    if (!placements) return [];\n    let allPlacements = [\n        ...placements\n    ];\n    // Get placements for children recursively\n    for (const placement of placements){\n        const childPlacements = await getAllNetworkPlacementsFallback(placement.child_id, maxDepth, currentDepth + 1);\n        allPlacements = allPlacements.concat(childPlacements);\n    }\n    return allPlacements;\n}\nasync function getAllActiveSubscriptions() {\n    const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.from(\"user_subscriptions\").select(`\n      user_id,\n      id,\n      status,\n      expires_at,\n      package:subscription_packages(\n        name,\n        price,\n        currency\n      )\n    `).eq(\"status\", \"active\").gte(\"expires_at\", new Date().toISOString()).order(\"expires_at\", {\n        ascending: false\n    });\n    return data || [];\n}\nfunction calculateUserStats(userId, allUsers, allPlacements) {\n    // Calculate direct referrals from users table (referred_by_id relationship)\n    const directReferrals = allUsers.filter((user)=>user.referred_by_id === userId).length;\n    // Calculate total downline using placement data (tree structure)\n    const getDownlineCount = (parentId, visited = new Set())=>{\n        if (visited.has(parentId)) return 0;\n        visited.add(parentId);\n        const children = allPlacements.filter((p)=>p.parent_id === parentId);\n        let count = children.length;\n        for (const child of children){\n            count += getDownlineCount(child.child_id, visited);\n        }\n        return count;\n    };\n    const totalDownline = getDownlineCount(userId);\n    // Debug logging to see what's happening\n    if (true) {\n        console.log(`Stats for user ${userId}:`, {\n            directReferrals,\n            totalDownline,\n            totalUsersInNetwork: allUsers.length,\n            totalPlacementsInNetwork: allPlacements.length,\n            userReferrals: allUsers.filter((user)=>user.referred_by_id === userId).map((u)=>u.email),\n            userPlacements: allPlacements.filter((p)=>p.parent_id === userId).map((p)=>p.child_id)\n        });\n    }\n    return {\n        directReferrals,\n        totalDownline,\n        totalCommission: 0 // Will be calculated separately if needed\n    };\n}\n// Get all introducers (users who referred others in the network)\nasync function getAllIntroducers(rootUserId, maxDepth) {\n    try {\n        // First get all users in the network\n        const networkUsers = await getAllNetworkUsers(rootUserId, maxDepth);\n        // Get unique referred_by_ids from network users\n        const referredByIds = [\n            ...new Set(networkUsers.map((user)=>user.referred_by_id).filter((id)=>id !== null && id !== undefined))\n        ];\n        if (referredByIds.length === 0) {\n            return [];\n        }\n        // Fetch introducer details\n        const { data: introducers, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.from(\"users\").select(\"id, full_name, email\").in(\"id\", referredByIds);\n        if (error) {\n            console.error(\"Error fetching introducers:\", error);\n            return [];\n        }\n        return introducers || [];\n    } catch (error) {\n        console.error(\"Error in getAllIntroducers:\", error);\n        return [];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/network-tree/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/referralSystem.ts":
/*!********************************************!*\
  !*** ./src/lib/services/referralSystem.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReferralSystemService: () => (/* binding */ ReferralSystemService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n/**\n * ReferralSystemService - Manages the multi-level referral and commission system\n */ class ReferralSystemService {\n    /**\n   * Generate a unique referral code\n   */ static async generateReferralCode() {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"generate_referral_code\");\n        if (error) {\n            throw new Error(`Failed to generate referral code: ${error.message}`);\n        }\n        return data;\n    }\n    /**\n   * Validate referral code and get referrer information\n   */ static async validateReferralCode(code) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"referral_code\", code).eq(\"is_referral_active\", true).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return null // Code not found\n                    ;\n                }\n                throw new Error(`Failed to validate referral code: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error validating referral code:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Place user in referral hierarchy\n   */ static async placeUserInHierarchy(newUserId, referrerId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"place_user_in_hierarchy\", {\n                new_user_id: newUserId,\n                referrer_id: referrerId\n            });\n            if (error) {\n                throw new Error(`Failed to place user in hierarchy: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error placing user in hierarchy:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's referral statistics\n   */ static async getUserReferralStats(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"direct_referrals_count, total_downline_count, total_commission_earned, referral_level, referral_code, user_type\").eq(\"id\", userId).single();\n            if (error) {\n                throw new Error(`Failed to get referral stats: ${error.message}`);\n            }\n            let referralCode = data.referral_code;\n            // If user doesn't have a referral code, generate one\n            if (!referralCode) {\n                referralCode = await this.generateReferralCode();\n                // Update the user with the new referral code\n                const { error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                    referral_code: referralCode\n                }).eq(\"id\", userId);\n                if (updateError) {\n                    console.error(\"Error updating referral code:\", updateError);\n                // Don't throw error, just use the generated code\n                }\n            }\n            // Get actual direct referrals count from database (real-time)\n            const { count: actualDirectReferrals, error: directError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"referred_by_id\", userId);\n            if (directError) {\n                console.error(\"Error counting direct referrals:\", directError);\n            }\n            // Calculate total referrals based on user type using real-time data\n            let totalReferrals = 0;\n            let actualTotalDownline = 0;\n            if (data.user_type === \"user\") {\n                // For regular users, limit to level 10\n                totalReferrals = await this.getTotalReferralsWithLevelLimit(userId, 10);\n                actualTotalDownline = totalReferrals;\n            } else {\n                // For ZM, RSM, and OKDOI Head, calculate from referral_hierarchy table (real-time)\n                const { count: totalDownlineCount, error: hierarchyError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"referral_hierarchy\").select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"ancestor_id\", userId);\n                if (hierarchyError) {\n                    console.error(\"Error counting total referrals from hierarchy:\", hierarchyError);\n                    totalReferrals = actualDirectReferrals || 0;\n                    actualTotalDownline = actualDirectReferrals || 0;\n                } else {\n                    totalReferrals = totalDownlineCount || 0;\n                    actualTotalDownline = totalDownlineCount || 0;\n                }\n            }\n            return {\n                directReferrals: actualDirectReferrals || 0,\n                totalDownline: actualTotalDownline,\n                totalCommissionEarned: parseFloat(data.total_commission_earned) || 0,\n                currentLevel: data.referral_level || 0,\n                totalReferrals: totalReferrals,\n                referralCode: referralCode\n            };\n        } catch (error) {\n            console.error(\"Error getting referral stats:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get total referrals with level limit for regular users\n   */ static async getTotalReferralsWithLevelLimit(userId, maxLevel) {\n        try {\n            // Get all referrals within the level limit using referral_hierarchy\n            const { count, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_HIERARCHY).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"ancestor_id\", userId).lte(\"level_difference\", maxLevel);\n            if (error) {\n                console.error(\"Error counting referrals with level limit:\", error);\n                return 0;\n            }\n            return count || 0;\n        } catch (error) {\n            console.error(\"Error getting total referrals with level limit:\", error);\n            return 0;\n        }\n    }\n    /**\n   * Get user's direct referrals\n   */ static async getDirectReferrals(userId) {\n        try {\n            // First get the placement records\n            const { data: placements, error: placementError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(\"child_id, position, created_at\").eq(\"parent_id\", userId).order(\"position\");\n            if (placementError) {\n                throw new Error(`Failed to get referral placements: ${placementError.message}`);\n            }\n            if (!placements || placements.length === 0) {\n                return [];\n            }\n            // Get the user details for each child\n            const childIds = placements.map((p)=>p.child_id);\n            const { data: users, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").in(\"id\", childIds);\n            if (userError) {\n                throw new Error(`Failed to get referral users: ${userError.message}`);\n            }\n            // Sort users by placement position\n            const sortedUsers = users?.sort((a, b)=>{\n                const positionA = placements.find((p)=>p.child_id === a.id)?.position || 0;\n                const positionB = placements.find((p)=>p.child_id === b.id)?.position || 0;\n                return positionA - positionB;\n            }) || [];\n            return sortedUsers;\n        } catch (error) {\n            console.error(\"Error getting direct referrals:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's referral tree (up to specified depth)\n   */ static async getReferralTree(userId, maxDepth = 3) {\n        try {\n            // Get the root user\n            const { data: rootUser, error: rootError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"id\", userId).single();\n            if (rootError) {\n                throw new Error(`Failed to get root user: ${rootError.message}`);\n            }\n            // Build the tree recursively using referral_placements (tree structure)\n            const buildTree = async (user, currentDepth)=>{\n                const children = [];\n                if (currentDepth < maxDepth) {\n                    const { data: placements, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(`\n              position,\n              placement_type,\n              child:child_id(\n                *,\n                active_subscription:user_subscriptions!left(\n                  id,\n                  status,\n                  expires_at,\n                  package:subscription_packages(\n                    name,\n                    price,\n                    currency\n                  )\n                )\n              )\n            `).eq(\"parent_id\", user.id).order(\"position\");\n                    if (!error && placements) {\n                        for (const placement of placements){\n                            const childNode = await buildTree(placement.child, currentDepth + 1);\n                            childNode.position = placement.position;\n                            children.push(childNode);\n                        }\n                    }\n                }\n                return {\n                    user,\n                    children,\n                    level: currentDepth,\n                    position: 0 // Will be set by parent\n                };\n            };\n            return await buildTree(rootUser, 0);\n        } catch (error) {\n            console.error(\"Error getting referral tree:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's commission transactions\n   */ static async getCommissionTransactions(userId, page = 1, limit = 20) {\n        try {\n            const offset = (page - 1) * limit;\n            const [dataResult, countResult] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\").eq(\"beneficiary_id\", userId).order(\"created_at\", {\n                    ascending: false\n                }).range(offset, offset + limit - 1),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"beneficiary_id\", userId)\n            ]);\n            if (dataResult.error) {\n                throw new Error(`Failed to get commission transactions: ${dataResult.error.message}`);\n            }\n            if (countResult.error) {\n                throw new Error(`Failed to count commission transactions: ${countResult.error.message}`);\n            }\n            return {\n                transactions: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting commission transactions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create Zonal Manager\n   */ static async createZonalManager(userId, zoneName, zoneDescription, assignedDistricts = [], createdBy) {\n        try {\n            // First update user type\n            const { error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"zonal_manager\"\n            }).eq(\"id\", userId);\n            if (userError) {\n                throw new Error(`Failed to update user type: ${userError.message}`);\n            }\n            // Create zonal manager record\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).insert({\n                user_id: userId,\n                zone_name: zoneName,\n                zone_description: zoneDescription,\n                assigned_districts: assignedDistricts,\n                created_by: createdBy\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to create zonal manager: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error creating zonal manager:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Upgrade user to RSM\n   */ static async upgradeToRSM(userId, zonalManagerId, regionName, upgradedBy) {\n        try {\n            // Validate that user exists and is eligible\n            const { data: userData, error: userFetchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"user_type, referred_by_id\").eq(\"id\", userId).single();\n            if (userFetchError) {\n                throw new Error(`Failed to get user data: ${userFetchError.message}`);\n            }\n            if (userData.user_type !== \"user\") {\n                throw new Error(\"Only regular users can be upgraded to RSM\");\n            }\n            // If ZM is specified, validate the relationship\n            if (zonalManagerId) {\n                const { data: zmData, error: zmError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).select(\"user_id\").eq(\"id\", zonalManagerId).single();\n                if (zmError) {\n                    throw new Error(`Failed to get ZM data: ${zmError.message}`);\n                }\n                // Check if user is in the ZM's network (not just direct downline)\n                const { data: hierarchyCheck, error: hierarchyError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_HIERARCHY).select(\"id\").eq(\"user_id\", userId).eq(\"ancestor_id\", zmData.user_id).single();\n                if (hierarchyError && hierarchyError.code !== \"PGRST116\") {\n                    throw new Error(`Failed to check user hierarchy: ${hierarchyError.message}`);\n                }\n                if (!hierarchyCheck) {\n                    throw new Error(\"User must be in the network of the selected Zonal Manager\");\n                }\n                // Check if user is already an RSM under this ZM\n                const { data: existingRSM, error: rsmCheckError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(\"id\").eq(\"zonal_manager_id\", zonalManagerId).eq(\"user_id\", userId).eq(\"is_active\", true).single();\n                if (rsmCheckError && rsmCheckError.code !== \"PGRST116\") {\n                    throw new Error(`Failed to check existing RSM: ${rsmCheckError.message}`);\n                }\n                if (existingRSM) {\n                    throw new Error(\"This user is already an RSM under the selected Zonal Manager\");\n                }\n                // Check if the user's direct line already has an RSM\n                const { data: directReferrals, error: directError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"id\").eq(\"referred_by_id\", zmData.user_id);\n                if (directError) {\n                    throw new Error(`Failed to get direct referrals: ${directError.message}`);\n                }\n                const directReferralIds = directReferrals?.map((user)=>user.id) || [];\n                const userDirectLine = await ReferralSystemService.findDirectLineForUser(userId, zmData.user_id, directReferralIds);\n                if (!userDirectLine) {\n                    throw new Error(\"Could not determine user's direct line under the ZM\");\n                }\n                // Check if this direct line already has an RSM\n                const { data: existingRSMs, error: existingRSMError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(`\n            user_id,\n            user:user_id(referred_by_id)\n          `).eq(\"zonal_manager_id\", zonalManagerId).eq(\"is_active\", true);\n                if (existingRSMError) {\n                    throw new Error(`Failed to check existing RSMs: ${existingRSMError.message}`);\n                }\n                if (existingRSMs) {\n                    for (const rsm of existingRSMs){\n                        const rsmDirectLine = await ReferralSystemService.findDirectLineForUser(rsm.user_id, zmData.user_id, directReferralIds);\n                        if (rsmDirectLine === userDirectLine) {\n                            throw new Error(\"This direct line already has an RSM assigned\");\n                        }\n                    }\n                }\n            }\n            // Update user type\n            const { error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"rsm\"\n            }).eq(\"id\", userId);\n            if (userError) {\n                throw new Error(`Failed to update user type: ${userError.message}`);\n            }\n            // Create RSM record\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).insert({\n                user_id: userId,\n                zonal_manager_id: zonalManagerId,\n                region_name: regionName,\n                upgraded_by: upgradedBy\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to create RSM: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error upgrading to RSM:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all Zonal Managers\n   */ static async getZonalManagers() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).select(`\n          *,\n          user:user_id(*)\n        `).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(`Failed to get zonal managers: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting zonal managers:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all Regional Sales Managers\n   */ static async getRegionalSalesManagers() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(`\n          *,\n          user:user_id(*),\n          zonal_manager:zonal_manager_id(*)\n        `).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(`Failed to get regional sales managers: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting regional sales managers:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Search users for ZM/RSM creation\n   */ static async searchUsers(searchTerm, limit = 20) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").or(`full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`).eq(\"user_type\", \"user\") // Only regular users can be upgraded\n            .limit(limit);\n            if (error) {\n                throw new Error(`Failed to search users: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error searching users:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get eligible users for RSM upgrade under a specific ZM\n   * Rules:\n   * - Must be in the ZM's entire network (not just direct referrals)\n   * - Must not already be RSM or ZM\n   * - ZM can have only 1 RSM per direct downline (direct referral line)\n   */ static async getEligibleUsersForRSMUpgrade(zonalManagerId) {\n        try {\n            // Validate input\n            if (!zonalManagerId || typeof zonalManagerId !== \"string\") {\n                throw new Error(\"Invalid zonal manager ID provided\");\n            }\n            // First get the ZM's user_id (use admin client for RLS bypass)\n            const { data: zmData, error: zmError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).select(\"user_id\").eq(\"id\", zonalManagerId).single();\n            if (zmError) {\n                console.error(\"ZM data query error:\", zmError);\n                throw new Error(`Failed to get ZM data: ${zmError.message}`);\n            }\n            if (!zmData || !zmData.user_id) {\n                throw new Error(\"Zonal Manager not found or invalid\");\n            }\n            // Get all users in the ZM's network using referral_hierarchy (use admin client for RLS bypass)\n            // Use inner join to ensure we only get users that actually exist\n            const { data: networkUsers, error: networkError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_HIERARCHY).select(`\n          user_id,\n          level_difference,\n          user:user_id!inner(\n            id,\n            email,\n            full_name,\n            user_type,\n            referred_by_id\n          )\n        `).eq(\"ancestor_id\", zmData.user_id).order(\"level_difference\", {\n                ascending: true\n            });\n            if (networkError) {\n                console.error(\"Network users query error:\", networkError);\n                throw new Error(`Failed to get network users: ${networkError.message}`);\n            }\n            console.log(`Found ${networkUsers?.length || 0} network users for ZM ${zmData.user_id}`);\n            // Validate network users data\n            if (!networkUsers || !Array.isArray(networkUsers)) {\n                console.warn(\"No network users found or invalid data structure\");\n                return [];\n            }\n            // Debug: Log any null user entries and filter them out\n            const validNetworkUsers = networkUsers.filter((nu)=>{\n                if (!nu || !nu.user || !nu.user.id) {\n                    console.warn(\"Filtering out network user with null/invalid user data:\", nu);\n                    return false;\n                }\n                return true;\n            });\n            console.log(`Valid network users after filtering: ${validNetworkUsers.length}`);\n            if (validNetworkUsers.length === 0) {\n                console.log(\"No valid network users found after filtering\");\n                return [];\n            }\n            // Get direct referrals of ZM to understand direct lines (use admin client for RLS bypass)\n            const { data: directReferrals, error: directError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"id\").eq(\"referred_by_id\", zmData.user_id);\n            if (directError) {\n                console.error(\"Direct referrals query error:\", directError);\n                throw new Error(`Failed to get direct referrals: ${directError.message}`);\n            }\n            const directReferralIds = directReferrals?.map((user)=>user?.id).filter(Boolean) || [];\n            console.log(`Found ${directReferralIds.length} direct referrals for ZM`);\n            // Get existing RSMs under this ZM to check which direct lines already have RSMs (use admin client for RLS bypass)\n            const { data: existingRSMs, error: rsmError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(`\n          user_id,\n          user:user_id(referred_by_id)\n        `).eq(\"zonal_manager_id\", zonalManagerId).eq(\"is_active\", true);\n            if (rsmError) {\n                console.error(\"Error getting existing RSMs:\", rsmError);\n            // Don't throw here, just log and continue with empty array\n            }\n            // Find which direct lines already have RSMs\n            const directLinesWithRSM = new Set();\n            if (existingRSMs && Array.isArray(existingRSMs)) {\n                for (const rsm of existingRSMs){\n                    try {\n                        if (rsm && rsm.user_id) {\n                            // Find which direct line this RSM belongs to\n                            const rsmDirectLine = await ReferralSystemService.findDirectLineForUser(rsm.user_id, zmData.user_id, directReferralIds);\n                            if (rsmDirectLine) {\n                                directLinesWithRSM.add(rsmDirectLine);\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Error processing existing RSM:\", rsm, error);\n                    // Continue processing other RSMs\n                    }\n                }\n            }\n            // Filter eligible users\n            const eligibleUsers = [];\n            const existingRSMUserIds = existingRSMs?.map((rsm)=>rsm?.user_id).filter(Boolean) || [];\n            console.log(`Processing ${validNetworkUsers.length} network users for eligibility`);\n            for (const networkUser of validNetworkUsers){\n                try {\n                    const user = networkUser.user;\n                    // Double-check user data validity (already filtered above, but being extra safe)\n                    if (!user || !user.id || typeof user.id !== \"string\") {\n                        console.warn(\"Skipping network user with invalid user data:\", networkUser);\n                        continue;\n                    }\n                    // Skip if already RSM or ZM - only regular users can be upgraded\n                    if (existingRSMUserIds.includes(user.id) || user.user_type !== \"user\") {\n                        console.log(`Skipping user ${user.id} - already RSM/ZM or not regular user (type: ${user.user_type})`);\n                        continue;\n                    }\n                    // Find which direct line this user belongs to\n                    const userDirectLine = await ReferralSystemService.findDirectLineForUser(user.id, zmData.user_id, directReferralIds);\n                    // Only include if the direct line doesn't already have an RSM\n                    if (userDirectLine && !directLinesWithRSM.has(userDirectLine)) {\n                        // Ensure user has required fields for display\n                        const eligibleUser = {\n                            ...user,\n                            full_name: user.full_name || \"N/A\",\n                            email: user.email || \"N/A\"\n                        };\n                        eligibleUsers.push(eligibleUser);\n                        console.log(`Added eligible user: ${eligibleUser.id} (${eligibleUser.full_name})`);\n                    } else {\n                        console.log(`Skipping user ${user.id} - direct line ${userDirectLine} already has RSM or user not in direct line`);\n                    }\n                } catch (error) {\n                    console.error(\"Error processing network user:\", networkUser, error);\n                    continue;\n                }\n            }\n            console.log(`Found ${eligibleUsers.length} eligible users for RSM upgrade`);\n            return eligibleUsers;\n        } catch (error) {\n            console.error(\"Error getting eligible users for RSM upgrade:\", error);\n            // Return empty array instead of throwing to prevent UI crashes\n            return [];\n        }\n    }\n    /**\n   * Helper method to find which direct line a user belongs to under a ZM\n   */ static async findDirectLineForUser(userId, zmUserId, directReferralIds) {\n        try {\n            // Validate inputs\n            if (!userId || !zmUserId || !Array.isArray(directReferralIds)) {\n                console.warn(\"Invalid parameters for findDirectLineForUser:\", {\n                    userId,\n                    zmUserId,\n                    directReferralIds\n                });\n                return null;\n            }\n            // Get the user's referral path or trace upwards (use admin client for RLS bypass)\n            const { data: userData, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"referral_path, referred_by_id\").eq(\"id\", userId).single();\n            if (error) {\n                console.warn(`Error getting user data for ${userId}:`, error);\n                return null;\n            }\n            if (!userData) {\n                console.warn(`No user data found for ${userId}`);\n                return null;\n            }\n            // If user is a direct referral of ZM\n            if (userData.referred_by_id === zmUserId) {\n                return userId;\n            }\n            // Parse referral path to find the direct line\n            if (userData.referral_path && typeof userData.referral_path === \"string\") {\n                try {\n                    const pathIds = userData.referral_path.split(\",\").filter(Boolean);\n                    const zmIndex = pathIds.indexOf(zmUserId);\n                    if (zmIndex !== -1 && zmIndex < pathIds.length - 1) {\n                        const directLineId = pathIds[zmIndex + 1];\n                        if (directReferralIds.includes(directLineId)) {\n                            return directLineId;\n                        }\n                    }\n                } catch (pathError) {\n                    console.warn(\"Error parsing referral path:\", userData.referral_path, pathError);\n                }\n            }\n            // Fallback: trace upwards through referral hierarchy\n            let currentUserId = userId;\n            let currentReferredBy = userData.referred_by_id;\n            let iterations = 0;\n            const maxIterations = 20 // Prevent infinite loops\n            ;\n            while(currentReferredBy && currentReferredBy !== zmUserId && iterations < maxIterations){\n                iterations++;\n                try {\n                    const { data: parentData, error: parentError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"referred_by_id\").eq(\"id\", currentReferredBy).single();\n                    if (parentError || !parentData) {\n                        console.warn(`Error or no data for parent user ${currentReferredBy}:`, parentError);\n                        break;\n                    }\n                    currentUserId = currentReferredBy;\n                    currentReferredBy = parentData.referred_by_id;\n                } catch (parentError) {\n                    console.warn(\"Error querying parent user:\", parentError);\n                    break;\n                }\n            }\n            if (iterations >= maxIterations) {\n                console.warn(`Max iterations reached while tracing referral hierarchy for user ${userId}`);\n            }\n            // If we reached the ZM, the current user is the direct line\n            if (currentReferredBy === zmUserId && directReferralIds.includes(currentUserId)) {\n                return currentUserId;\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error finding direct line for user:\", {\n                userId,\n                zmUserId,\n                error\n            });\n            return null;\n        }\n    }\n    /**\n   * Get referral system statistics\n   */ static async getReferralSystemStats() {\n        try {\n            const [totalUsersResult, zonalManagersResult, regionalManagersResult, totalReferralsResult, activeCodesResult] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"is_active\", true),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"is_active\", true),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).not(\"referral_code\", \"is\", null)\n            ]);\n            return {\n                totalUsers: totalUsersResult.count || 0,\n                zonalManagers: zonalManagersResult.count || 0,\n                regionalManagers: regionalManagersResult.count || 0,\n                totalReferrals: totalReferralsResult.count || 0,\n                activeReferralCodes: activeCodesResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting referral system stats:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Deactivate Zonal Manager\n   */ static async deactivateZonalManager(zmId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).update({\n                is_active: false\n            }).eq(\"id\", zmId);\n            if (error) {\n                throw new Error(`Failed to deactivate zonal manager: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error deactivating zonal manager:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Deactivate Regional Sales Manager\n   */ static async deactivateRSM(rsmId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).update({\n                is_active: false\n            }).eq(\"id\", rsmId);\n            if (error) {\n                throw new Error(`Failed to deactivate RSM: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error deactivating RSM:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create or get OKDOI Head user\n   */ static async createOKDOIHead(email = \"<EMAIL>\", fullName = \"OKDOI Head\", phone) {\n        try {\n            // Check if OKDOI Head already exists using admin client\n            const { data: existingHead, error: checkError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"user_type\", \"okdoi_head\").single();\n            if (existingHead && !checkError) {\n                return existingHead;\n            }\n            // Generate referral code first\n            const referralCode = await this.generateReferralCode();\n            // Create auth user first using admin auth API\n            const { data: authUser, error: authError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.createUser({\n                email,\n                password: crypto.randomUUID(),\n                email_confirm: true,\n                user_metadata: {\n                    full_name: fullName,\n                    phone: phone || null\n                }\n            });\n            if (authError || !authUser.user) {\n                throw new Error(`Failed to create auth user: ${authError?.message}`);\n            }\n            // Create OKDOI Head user in public.users table using the auth user ID\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).insert({\n                id: authUser.user.id,\n                email,\n                full_name: fullName,\n                phone,\n                user_type: \"okdoi_head\",\n                role: \"admin\",\n                is_verified: true,\n                is_referral_active: true,\n                referral_level: 0,\n                referral_path: \"\",\n                direct_referrals_count: 0,\n                total_downline_count: 0,\n                total_commission_earned: 0,\n                referral_code: referralCode,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            }).select().single();\n            if (error) {\n                // If user creation fails, clean up the auth user\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.deleteUser(authUser.user.id);\n                throw new Error(`Failed to create OKDOI Head: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error creating OKDOI Head:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Assign OKDOI Head role to existing user\n   */ static async assignOKDOIHeadToUser(userId) {\n        try {\n            // Check if OKDOI Head already exists\n            const existingHead = await this.getOKDOIHead();\n            if (existingHead) {\n                throw new Error(\"OKDOI Head already exists. Only one OKDOI Head is allowed.\");\n            }\n            // Get the user to be assigned\n            const { data: user, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"id\", userId).single();\n            if (userError || !user) {\n                throw new Error(\"User not found\");\n            }\n            // Generate referral code if user doesn't have one\n            let referralCode = user.referral_code;\n            if (!referralCode) {\n                referralCode = await this.generateReferralCode();\n            }\n            // Update user to OKDOI Head\n            const { data: updatedUser, error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"okdoi_head\",\n                role: \"admin\",\n                is_verified: true,\n                is_referral_active: true,\n                referral_level: 0,\n                referral_path: \"\",\n                direct_referrals_count: 0,\n                total_downline_count: 0,\n                total_commission_earned: 0,\n                referral_code: referralCode,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", userId).select().single();\n            if (updateError) {\n                throw new Error(`Failed to assign OKDOI Head: ${updateError.message}`);\n            }\n            return updatedUser;\n        } catch (error) {\n            console.error(\"Error assigning OKDOI Head:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get OKDOI Head user\n   */ static async getOKDOIHead() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"user_type\", \"okdoi_head\").single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return null // No OKDOI Head found\n                    ;\n                }\n                throw new Error(`Failed to get OKDOI Head: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error getting OKDOI Head:\", error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/referralSystem.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Environment variables with validation\nconst supabaseUrl = \"https://vnmydqbwjjufnxngpnqo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Validate required environment variables\nif (!supabaseUrl) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n}\nif (!supabaseAnonKey) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\nif (!supabaseServiceRoleKey) {\n    console.warn(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable - admin functions will not work\");\n}\n// Create browser client with error handling\nlet supabase;\ntry {\n    supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            get (name) {\n                if (typeof document !== \"undefined\") {\n                    const value = document.cookie.split(\"; \").find((row)=>row.startsWith(`${name}=`))?.split(\"=\")[1];\n                    return value ? decodeURIComponent(value) : undefined;\n                }\n                return undefined;\n            },\n            set (name, value, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=${encodeURIComponent(value)}`;\n                    if (options?.maxAge) cookieString += `; max-age=${options.maxAge}`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    if (options?.secure) cookieString += \"; secure\";\n                    if (options?.httpOnly) cookieString += \"; httponly\";\n                    if (options?.sameSite) cookieString += `; samesite=${options.sameSite}`;\n                    document.cookie = cookieString;\n                }\n            },\n            remove (name, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    document.cookie = cookieString;\n                }\n            }\n        }\n    });\n} catch (error) {\n    console.error(\"Failed to create Supabase browser client:\", error);\n    // Fallback to basic client without SSR\n    supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Admin client with service role key for bypassing RLS\n// Note: This will be null on client-side for security reasons\nconst supabaseAdmin = supabaseServiceRoleKey ? (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceRoleKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n\n// Database table names\nconst TABLES = {\n    CATEGORIES: \"categories\",\n    SUBCATEGORIES: \"subcategories\",\n    ADS: \"ads\",\n    USERS: \"users\",\n    AD_IMAGES: \"ad_images\",\n    DISTRICTS: \"districts\",\n    CITIES: \"cities\",\n    USER_FAVORITES: \"user_favorites\",\n    VENDOR_SHOPS: \"vendor_shops\",\n    SHOP_PRODUCTS: \"shop_products\",\n    SHOP_PRODUCT_IMAGES: \"shop_product_images\",\n    SHOP_REVIEWS: \"shop_reviews\",\n    SHOP_FOLLOWERS: \"shop_followers\",\n    SHOP_CATEGORIES: \"shop_categories\",\n    SHOP_SUBCATEGORIES: \"shop_subcategories\",\n    PRODUCT_REVIEWS: \"product_reviews\",\n    CHAT_CONVERSATIONS: \"chat_conversations\",\n    CHAT_MESSAGES: \"chat_messages\",\n    USER_WALLETS: \"user_wallets\",\n    WALLET_TRANSACTIONS: \"wallet_transactions\",\n    P2P_TRANSFERS: \"p2p_transfers\",\n    DEPOSIT_REQUESTS: \"deposit_requests\",\n    WITHDRAWAL_REQUESTS: \"withdrawal_requests\",\n    SUBSCRIPTION_PACKAGES: \"subscription_packages\",\n    USER_SUBSCRIPTIONS: \"user_subscriptions\",\n    AD_BOOSTS: \"ad_boosts\",\n    BOOST_PACKAGES: \"boost_packages\",\n    // Order Management System\n    CART_ITEMS: \"cart_items\",\n    SHOP_ORDERS: \"shop_orders\",\n    ORDER_ITEMS: \"order_items\",\n    ORDER_STATUS_HISTORY: \"order_status_history\",\n    // Merchant Wallet System\n    MERCHANT_WALLETS: \"merchant_wallets\",\n    MERCHANT_WALLET_TRANSACTIONS: \"merchant_wallet_transactions\",\n    MERCHANT_TO_MAIN_TRANSFERS: \"merchant_to_main_transfers\",\n    // Referral & Commission System\n    REFERRAL_HIERARCHY: \"referral_hierarchy\",\n    COMMISSION_STRUCTURE: \"commission_structure\",\n    COMMISSION_TRANSACTIONS: \"commission_transactions\",\n    REFERRAL_PLACEMENTS: \"referral_placements\",\n    // KYC System\n    KYC_SUBMISSIONS: \"kyc_submissions\",\n    KYC_STATUS_HISTORY: \"kyc_status_history\",\n    KYC_DOCUMENT_TYPES: \"kyc_document_types\",\n    ZONAL_MANAGERS: \"zonal_managers\",\n    REGIONAL_SALES_MANAGERS: \"regional_sales_managers\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fnetwork-tree%2Froute&page=%2Fapi%2Fadmin%2Fnetwork-tree%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fnetwork-tree%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();