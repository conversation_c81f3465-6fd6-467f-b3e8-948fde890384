'use client'

import { useState, useEffect } from 'react'
import {
  ChevronDown,
  ChevronRight,
  Crown,
  Users,
  TrendingUp,
  Mail,
  Phone,
  Calendar,
  Network,
  User as UserIcon,
  Shield,
  MapPin,
  Zap,
  Star,
  Award,
  Target,
  Eye,
  MoreHorizontal,
  Plus,
  Minus
} from 'lucide-react'
import { User } from '@/types'

export interface NetworkTreeNode {
  user: User
  children: NetworkTreeNode[]
  level: number
  position: number
  stats?: {
    directReferrals: number
    totalDownline: number
    totalCommission: number
  }
}

interface NetworkTreeProps {
  rootNode: NetworkTreeNode | null
  loading?: boolean
  onNodeClick?: (user: User) => void
  maxDepth?: number
  className?: string
}

interface TreeNodeProps {
  node: NetworkTreeNode
  onNodeClick?: (user: User) => void
  isExpanded: boolean
  onToggleExpand: (nodeId: string) => void
  maxDepth: number
}

function TreeNode({ node, onNodeClick, isExpanded, onToggleExpand, maxDepth }: TreeNodeProps) {
  const [isHovered, setIsHovered] = useState(false)
  const hasChildren = node.children && node.children.length > 0
  const canExpand = hasChildren && (maxDepth >= 999 || node.level < maxDepth)

  const getUserTypeIcon = (userType: string) => {
    switch (userType) {
      case 'okdoi_head':
        return <Crown className="h-5 w-5 text-amber-600" />
      case 'zonal_manager':
        return <Shield className="h-5 w-5 text-purple-600" />
      case 'rsm':
        return <Network className="h-5 w-5 text-green-600" />
      default:
        return <UserIcon className="h-5 w-5 text-blue-600" />
    }
  }

  const getNodeStyle = (userType: string) => {
    switch (userType) {
      case 'okdoi_head':
        return {
          container: 'bg-gradient-to-br from-green-400 to-green-600 text-white shadow-lg border-2 border-green-300',
          size: 'w-48 h-24',
          textSize: 'text-lg font-bold'
        }
      case 'zonal_manager':
        return {
          container: 'bg-gradient-to-br from-purple-300 to-purple-500 text-white shadow-md border border-purple-200',
          size: 'w-40 h-20',
          textSize: 'text-base font-semibold'
        }
      case 'rsm':
        return {
          container: 'bg-gradient-to-br from-teal-300 to-teal-500 text-white shadow-md border border-teal-200',
          size: 'w-32 h-16',
          textSize: 'text-sm font-medium'
        }
      default:
        return {
          container: 'bg-gradient-to-br from-blue-300 to-blue-500 text-white shadow-sm border border-blue-200',
          size: 'w-28 h-14',
          textSize: 'text-xs font-medium'
        }
    }
  }

  const getUserTypeLabel = (userType: string) => {
    switch (userType) {
      case 'okdoi_head':
        return 'OKDOI (Main Head)'
      case 'zonal_manager':
        return 'Zonal Manager'
      case 'rsm':
        return 'RSM'
      default:
        return 'User'
    }
  }

  const style = getNodeStyle(node.user.user_type || 'user')

  // For OKDOI Head, show as central node
  if (node.user.user_type === 'okdoi_head') {
    return (
      <div className="flex flex-col items-center space-y-8">
        {/* OKDOI Head Node */}
        <div className="relative">
          <div
            className={`${style.container} ${style.size} rounded-lg flex flex-col items-center justify-center cursor-pointer transform transition-all duration-200 hover:scale-105 ${
              isHovered ? 'shadow-xl' : ''
            }`}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={() => onNodeClick?.(node.user)}
          >
            <div className="flex items-center space-x-2 mb-1">
              {getUserTypeIcon(node.user.user_type || 'user')}
              <span className={style.textSize}>OKDOI</span>
            </div>
            <span className="text-sm opacity-90">(Main Head)</span>
            {hasChildren && (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onToggleExpand(node.user.id)
                }}
                className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 bg-white rounded-full p-1 shadow-md hover:shadow-lg transition-shadow"
              >
                {isExpanded ? (
                  <Minus className="h-4 w-4 text-gray-600" />
                ) : (
                  <Plus className="h-4 w-4 text-gray-600" />
                )}
              </button>
            )}
          </div>
        </div>

  return (
    <div className="relative">
      {/* Connection Line */}
      {node.level > 0 && (
        <div className={`absolute -left-8 top-8 w-8 h-px border-t-2 ${getConnectionLineColor(node.user.user_type || 'user')} opacity-60`}></div>
      )}

      {/* Node Card */}
      <div
        className={`
          relative border-2 rounded-xl p-6 mb-6 cursor-pointer transition-all duration-300 transform
          ${getUserTypeBg(node.user.user_type || 'user', isHovered)}
          backdrop-blur-sm
        `}
        onClick={() => onNodeClick?.(node.user)}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Expand/Collapse Button */}
        {canExpand && (
          <button
            onClick={(e) => {
              e.stopPropagation()
              onToggleExpand(node.user.id)
            }}
            className="absolute -right-3 -top-3 w-8 h-8 bg-white border-2 border-gray-300 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-110"
          >
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 text-gray-600" />
            ) : (
              <ChevronRight className="h-4 w-4 text-gray-600" />
            )}
          </button>
        )}

        {/* Node Actions */}
        <div className="absolute top-4 right-4">
          <button className="w-8 h-8 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-white shadow-sm">
            <Eye className="h-4 w-4 text-gray-600" />
          </button>
        </div>

        {/* User Info */}
        <div className="flex items-start space-x-4 group">
          {/* Avatar with Icon */}
          <div className="flex-shrink-0 relative">
            <div className="w-16 h-16 bg-gradient-to-br from-white to-gray-50 rounded-full border-3 border-white shadow-lg flex items-center justify-center">
              {node.user.avatar_url ? (
                <img
                  src={node.user.avatar_url}
                  alt={node.user.full_name || 'User'}
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <div className="w-full h-full rounded-full bg-gradient-to-br from-blue-100 to-indigo-200 flex items-center justify-center">
                  <span className="text-lg font-bold text-blue-700">
                    {(node.user.full_name || node.user.email || 'U').charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
            </div>
            {/* Rank Badge */}
            <div className="absolute -bottom-1 -right-1">
              <div className="w-8 h-8 rounded-full border-2 border-white shadow-lg flex items-center justify-center">
                {getUserTypeIcon(node.user.user_type || 'user')}
              </div>
            </div>
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-lg font-bold text-gray-900 truncate">
                {node.user.full_name || 'N/A'}
              </h3>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${
                node.user.user_type === 'okdoi_head' ? 'bg-amber-100 text-amber-800' :
                node.user.user_type === 'zonal_manager' ? 'bg-purple-100 text-purple-800' :
                node.user.user_type === 'rsm' ? 'bg-green-100 text-green-800' :
                'bg-blue-100 text-blue-800'
              }`}>
                {getUserTypeLabel(node.user.user_type || 'user')}
              </span>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center text-sm text-gray-700">
                <Mail className="h-4 w-4 mr-2 text-blue-500" />
                <span className="truncate font-medium">{node.user.email}</span>
              </div>

              {node.user.phone && (
                <div className="flex items-center text-sm text-gray-700">
                  <Phone className="h-4 w-4 mr-2 text-green-500" />
                  <span className="font-medium">{node.user.phone}</span>
                </div>
              )}

              {node.user.location && (
                <div className="flex items-center text-sm text-gray-700">
                  <MapPin className="h-4 w-4 mr-2 text-red-500" />
                  <span className="font-medium">{node.user.location}</span>
                </div>
              )}

              <div className="flex items-center text-sm text-gray-600">
                <Calendar className="h-4 w-4 mr-2 text-purple-500" />
                <span>Joined {new Date(node.user.created_at).toLocaleDateString()}</span>
              </div>
            </div>

            {/* Stats */}
            {node.stats && (
              <div className="mt-4 bg-white/50 backdrop-blur-sm rounded-lg p-3 border border-white/20">
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-1">
                      <Users className="h-4 w-4 text-blue-500 mr-1" />
                      <span className="text-lg font-bold text-blue-700">{node.stats.directReferrals}</span>
                    </div>
                    <div className="text-xs font-medium text-gray-600">Direct Referrals</div>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-1">
                      <Network className="h-4 w-4 text-purple-500 mr-1" />
                      <span className="text-lg font-bold text-purple-700">{node.stats.totalDownline}</span>
                    </div>
                    <div className="text-xs font-medium text-gray-600">Total Network</div>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-1">
                      <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-lg font-bold text-green-700">Rs {node.stats.totalCommission.toLocaleString()}</span>
                    </div>
                    <div className="text-xs font-medium text-gray-600">Total Earnings</div>
                  </div>
                </div>
              </div>
            )}

            {/* Referral Code */}
            {node.user.referral_code && (
              <div className="mt-3">
                <div className="flex items-center space-x-2">
                  <span className="text-xs font-medium text-gray-600">Referral Code:</span>
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-mono bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200">
                    <Star className="h-3 w-3 mr-1" />
                    {node.user.referral_code}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Children */}
      {canExpand && isExpanded && (
        <div className="ml-12 relative">
          {/* Vertical Connection Line */}
          <div className={`absolute -left-8 top-0 bottom-8 w-px border-l-2 ${getConnectionLineColor(node.user.user_type || 'user')} opacity-40`}></div>

          {node.children.map((child, index) => (
            <div key={child.user.id} className="relative animate-fadeIn" style={{ animationDelay: `${index * 150}ms` }}>
              {/* Horizontal Connection Line */}
              <div className={`absolute -left-8 top-8 w-8 h-px border-t-2 ${getConnectionLineColor(child.user.user_type || 'user')} opacity-60`}></div>

              <TreeNode
                node={child}
                onNodeClick={onNodeClick}
                isExpanded={isExpanded}
                onToggleExpand={onToggleExpand}
                maxDepth={maxDepth}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default function NetworkTree({
  rootNode,
  loading = false,
  onNodeClick,
  maxDepth = 999,
  className = ''
}: NetworkTreeProps) {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set())

  // Auto-expand root node
  useEffect(() => {
    if (rootNode) {
      setExpandedNodes(new Set([rootNode.user.id]))
    }
  }, [rootNode])

  const toggleExpand = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes)
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId)
    } else {
      newExpanded.add(nodeId)
    }
    setExpandedNodes(newExpanded)
  }

  if (loading) {
    return (
      <div className={`flex items-center justify-center py-16 ${className}`}>
        <div className="text-center">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-6"></div>
            <div className="absolute inset-0 rounded-full h-16 w-16 border-4 border-transparent border-r-purple-400 animate-spin mx-auto" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Network Tree</h3>
          <p className="text-gray-600">Building your referral network visualization...</p>
        </div>
      </div>
    )
  }

  if (!rootNode) {
    return (
      <div className={`text-center py-16 ${className}`}>
        <div className="relative mb-6">
          <div className="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full mx-auto flex items-center justify-center">
            <Network className="h-12 w-12 text-gray-400" />
          </div>
          <div className="absolute inset-0 w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full mx-auto opacity-0 animate-pulse"></div>
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-3">No Network Data Available</h3>
        <p className="text-gray-600 max-w-md mx-auto">
          No referral network found to display. Create your first referral to start building your network tree.
        </p>
      </div>
    )
  }

  return (
    <div className={`overflow-auto ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
              <Network className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Network Tree</h2>
              <p className="text-gray-600">Interactive referral network visualization</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-600">Total Network Size</div>
            <div className="text-2xl font-bold text-blue-600">
              {rootNode.stats ? rootNode.stats.totalDownline + 1 : 1}
            </div>
          </div>
        </div>
      </div>

      {/* Tree Content */}
      <div className="p-8 bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
        <div className="max-w-full">
          <TreeNode
            node={rootNode}
            onNodeClick={onNodeClick}
            isExpanded={expandedNodes.has(rootNode.user.id)}
            onToggleExpand={toggleExpand}
            maxDepth={maxDepth}
          />
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 bg-white">
        <div className="flex items-center justify-center space-x-6 text-sm text-gray-600">
          <div className="flex items-center space-x-2">
            <Crown className="h-4 w-4 text-amber-500" />
            <span>OKDOI Head</span>
          </div>
          <div className="flex items-center space-x-2">
            <Shield className="h-4 w-4 text-purple-500" />
            <span>Zonal Manager</span>
          </div>
          <div className="flex items-center space-x-2">
            <Network className="h-4 w-4 text-green-500" />
            <span>Regional Sales Manager</span>
          </div>
          <div className="flex items-center space-x-2">
            <UserIcon className="h-4 w-4 text-blue-500" />
            <span>User</span>
          </div>
        </div>
      </div>
    </div>
  )
}
