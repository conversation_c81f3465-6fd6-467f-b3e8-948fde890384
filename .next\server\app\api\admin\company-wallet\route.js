"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/company-wallet/route";
exports.ids = ["app/api/admin/company-wallet/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcompany-wallet%2Froute&page=%2Fapi%2Fadmin%2Fcompany-wallet%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcompany-wallet%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcompany-wallet%2Froute&page=%2Fapi%2Fadmin%2Fcompany-wallet%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcompany-wallet%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_okdoi_src_app_api_admin_company_wallet_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/company-wallet/route.ts */ \"(rsc)/./src/app/api/admin/company-wallet/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/company-wallet/route\",\n        pathname: \"/api/admin/company-wallet\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/company-wallet/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\api\\\\admin\\\\company-wallet\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_okdoi_src_app_api_admin_company_wallet_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/company-wallet/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcompany-wallet%2Froute&page=%2Fapi%2Fadmin%2Fcompany-wallet%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcompany-wallet%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/company-wallet/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/admin/company-wallet/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _lib_services_admin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/services/admin */ \"(rsc)/./src/lib/services/admin.ts\");\n\n\n\n// Create admin Supabase client with service role key\nconst supabaseAdmin = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://vnmydqbwjjufnxngpnqo.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\nasync function GET(request) {\n    try {\n        // Check admin authentication\n        const isAdmin = await _lib_services_admin__WEBPACK_IMPORTED_MODULE_1__.AdminService.isAdminServerSide();\n        if (!isAdmin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"20\");\n        const filterType = searchParams.get(\"filterType\") || \"all\";\n        const searchTerm = searchParams.get(\"searchTerm\") || \"\";\n        const dateFrom = searchParams.get(\"dateFrom\") || \"\";\n        const dateTo = searchParams.get(\"dateTo\") || \"\";\n        const offset = (page - 1) * limit;\n        // Fetch company wallet info\n        const { data: walletData, error: walletError } = await supabaseAdmin.from(\"company_wallet\").select(\"*\").single();\n        if (walletError && walletError.code !== \"PGRST116\") {\n            throw walletError;\n        }\n        // Build query for transactions\n        let query = supabaseAdmin.from(\"company_wallet_transactions\").select(\"*\", {\n            count: \"exact\"\n        }).order(\"created_at\", {\n            ascending: false\n        });\n        // Apply filters\n        if (filterType !== \"all\") {\n            query = query.eq(\"transaction_type\", filterType);\n        }\n        if (searchTerm) {\n            query = query.or(`transaction_id.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);\n        }\n        if (dateFrom) {\n            query = query.gte(\"created_at\", dateFrom);\n        }\n        if (dateTo) {\n            query = query.lte(\"created_at\", dateTo);\n        }\n        // Apply pagination\n        query = query.range(offset, offset + limit - 1);\n        const { data: transactions, error: transactionError, count } = await query;\n        if (transactionError) {\n            throw transactionError;\n        }\n        // Calculate stats\n        let stats = {\n            totalBalance: 0,\n            totalReceived: 0,\n            totalTransactions: 0,\n            monthlyProfit: 0,\n            averagePackageProfit: 0\n        };\n        if (walletData) {\n            // Get monthly profit (current month)\n            const currentMonth = new Date().toISOString().slice(0, 7) // YYYY-MM format\n            ;\n            const { data: monthlyData } = await supabaseAdmin.from(\"company_wallet_transactions\").select(\"amount\").gte(\"created_at\", currentMonth + \"-01\").lt(\"created_at\", new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1).toISOString());\n            const monthlyProfit = monthlyData?.reduce((sum, t)=>sum + parseFloat(t.amount), 0) || 0;\n            // Get total transactions count\n            const { count: totalTransactions } = await supabaseAdmin.from(\"company_wallet_transactions\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            });\n            // Calculate average package profit\n            const { data: allTransactions } = await supabaseAdmin.from(\"company_wallet_transactions\").select(\"amount\").eq(\"transaction_type\", \"company_profit\");\n            const averagePackageProfit = allTransactions && allTransactions.length > 0 ? allTransactions.reduce((sum, t)=>sum + parseFloat(t.amount), 0) / allTransactions.length : 0;\n            stats = {\n                totalBalance: parseFloat(walletData.balance),\n                totalReceived: parseFloat(walletData.total_received),\n                totalTransactions: totalTransactions || 0,\n                monthlyProfit,\n                averagePackageProfit\n            };\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                wallet: walletData,\n                transactions: transactions || [],\n                stats,\n                pagination: {\n                    page,\n                    limit,\n                    total: count || 0,\n                    totalPages: Math.ceil((count || 0) / limit)\n                }\n            }\n        });\n    } catch (error) {\n        console.error(\"GET /api/admin/company-wallet error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : \"Failed to fetch company wallet data\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/company-wallet/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/adminLogger.ts":
/*!********************************!*\
  !*** ./src/lib/adminLogger.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   adminLogger: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\okdoi\src\lib\adminLogger.ts#adminLogger`);


/***/ }),

/***/ "(rsc)/./src/lib/services/admin.ts":
/*!***********************************!*\
  !*** ./src/lib/services/admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminService: () => (/* binding */ AdminService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_adminLogger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/adminLogger */ \"(rsc)/./src/lib/adminLogger.ts\");\n\n\nclass AdminService {\n    static{\n        // Cache for admin status to avoid repeated database calls\n        this.adminStatusCache = new Map();\n    }\n    static{\n        this.CACHE_DURATION = 15 * 60 * 1000 // 15 minutes - increased for better performance\n        ;\n    }\n    static{\n        this.pendingAdminChecks = new Map() // Prevent concurrent checks\n        ;\n    }\n    /**\n   * Clear admin status cache and pending checks\n   */ static clearAdminCache() {\n        this.adminStatusCache.clear();\n        this.pendingAdminChecks.clear();\n    }\n    /**\n   * Debug function to check current user details\n   */ static async debugCurrentUser() {\n        console.log(\"=== AdminService Debug ===\");\n        const { data: { user }, error: authError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (authError) {\n            console.error(\"Auth error:\", authError);\n            return;\n        }\n        if (!user) {\n            console.log(\"No authenticated user found\");\n            return;\n        }\n        console.log(\"Auth user:\", {\n            id: user.id,\n            email: user.email,\n            created_at: user.created_at\n        });\n        // Check if user exists in users table\n        const { data: userData, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"id\", user.id).single();\n        if (userError) {\n            console.error(\"User table error:\", userError);\n            return;\n        }\n        console.log(\"Users table data:\", userData);\n        console.log(\"=== End Debug ===\");\n    }\n    /**\n   * Check if current user is admin (client-side) - Optimized with caching and concurrent request prevention\n   */ static async isAdmin() {\n        try {\n            // Get current session to determine user ID\n            const { data: { session } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (!session?.user) {\n                return false;\n            }\n            const userId = session.user.id;\n            // Check if there's already a pending check for this user\n            const pendingCheck = this.pendingAdminChecks.get(userId);\n            if (pendingCheck) {\n                _lib_adminLogger__WEBPACK_IMPORTED_MODULE_1__.adminLogger.debug(\"admin_check\", \"Using pending admin check\", {}, userId, session.user.email);\n                return await pendingCheck;\n            }\n            // Create new admin check promise\n            const adminCheckPromise = this.performAdminCheck();\n            this.pendingAdminChecks.set(userId, adminCheckPromise);\n            try {\n                const result = await adminCheckPromise;\n                return result;\n            } finally{\n                // Clean up pending check\n                this.pendingAdminChecks.delete(userId);\n            }\n        } catch (error) {\n            console.error(\"AdminService.isAdmin: Error:\", error);\n            // Clear cache on error\n            if (false) {}\n            return false;\n        }\n    }\n    static async performAdminCheck() {\n        try {\n            // Get current session (faster than getUser)\n            const { data: { session } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (!session?.user) {\n                return false;\n            }\n            const userId = session.user.id;\n            const now = Date.now();\n            // Check cache first with extended duration for better performance\n            const cached = this.adminStatusCache.get(userId);\n            if (cached && now - cached.timestamp < this.CACHE_DURATION) {\n                _lib_adminLogger__WEBPACK_IMPORTED_MODULE_1__.adminLogger.debug(\"admin_check\", \"Using cached admin status\", {\n                    isAdmin: cached.isAdmin,\n                    cacheAge: `${Math.round((now - cached.timestamp) / 1000)}s`\n                }, userId, session.user.email);\n                return cached.isAdmin;\n            }\n            _lib_adminLogger__WEBPACK_IMPORTED_MODULE_1__.adminLogger.info(\"admin_check\", \"Performing database admin check\", {}, userId, session.user.email);\n            // Single database query to check admin status with timeout\n            const timeoutPromise = new Promise((_, reject)=>{\n                setTimeout(()=>reject(new Error(\"Database query timeout\")), 2000);\n            });\n            const queryPromise = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"role, is_super_admin, email\").eq(\"id\", userId).single();\n            const { data: userData, error } = await Promise.race([\n                queryPromise,\n                timeoutPromise\n            ]);\n            if (error) {\n                _lib_adminLogger__WEBPACK_IMPORTED_MODULE_1__.adminLogger.error(\"admin_check\", \"Database query failed\", {\n                    error: error.message,\n                    code: error.code,\n                    details: error.details\n                }, userId, session.user.email);\n                // Return cached value if available, otherwise false\n                return cached?.isAdmin || false;\n            }\n            if (!userData) {\n                _lib_adminLogger__WEBPACK_IMPORTED_MODULE_1__.adminLogger.error(\"admin_check\", \"No user data found in database\", {}, userId, session.user.email);\n                return false;\n            }\n            const isAdmin = userData.role === \"admin\" || userData.is_super_admin === true;\n            _lib_adminLogger__WEBPACK_IMPORTED_MODULE_1__.adminLogger.info(\"admin_check\", \"Admin check completed successfully\", {\n                isAdmin,\n                role: userData.role,\n                is_super_admin: userData.is_super_admin\n            }, userId, userData.email);\n            // Cache the result with extended duration\n            this.adminStatusCache.set(userId, {\n                isAdmin,\n                timestamp: now\n            });\n            return isAdmin;\n        } catch (error) {\n            console.error(\"AdminService: Error in performAdminCheck:\", error);\n            // Return cached value if available, otherwise false\n            const { data: { session } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (session?.user) {\n                const cached = this.adminStatusCache.get(session.user.id);\n                return cached?.isAdmin || false;\n            }\n            return false;\n        }\n    }\n    /**\n   * Check if current user is admin (server-side compatible)\n   */ static async isAdminServerSide() {\n        console.log(\"AdminService.isAdminServerSide: Starting server-side admin check...\");\n        try {\n            // For server-side API routes, we need to use cookies\n            const { cookies } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/next\").then(__webpack_require__.bind(__webpack_require__, /*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\"));\n            const cookieStore = cookies();\n            // Create server client for auth\n            const { createServerClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\"));\n            const supabaseAuth = createServerClient(\"https://vnmydqbwjjufnxngpnqo.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\", {\n                cookies: {\n                    get (name) {\n                        return cookieStore.get(name)?.value;\n                    }\n                }\n            });\n            // Get the current user\n            const { data: { user }, error: authError } = await supabaseAuth.auth.getUser();\n            if (authError || !user) {\n                console.log(\"AdminService.isAdminServerSide: No user found or auth error:\", authError);\n                return false;\n            }\n            console.log(\"AdminService.isAdminServerSide: Checking user:\", user.email, \"ID:\", user.id);\n            // Import supabaseAdmin dynamically for server-side use\n            const { supabaseAdmin } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\"));\n            // Check if user is admin using admin client\n            if (!supabaseAdmin) {\n                console.error(\"AdminService.isAdminServerSide: Admin client not available\");\n                return false;\n            }\n            const { data: userData, error: userError } = await supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"role, is_super_admin, email\").eq(\"id\", user.id).single();\n            if (userError) {\n                console.error(\"AdminService.isAdminServerSide: User lookup failed:\", userError);\n                return false;\n            }\n            console.log(\"AdminService.isAdminServerSide: User data:\", userData);\n            const isAdmin = userData?.role === \"admin\" || userData?.is_super_admin === true;\n            console.log(\"AdminService.isAdminServerSide: Result:\", isAdmin);\n            return isAdmin;\n        } catch (error) {\n            console.error(\"AdminService.isAdminServerSide: Unexpected error:\", error);\n            return false;\n        }\n    }\n    /**\n   * Get admin dashboard statistics\n   */ static async getAdminStats() {\n        const [usersResult, adsResult, activeAdsResult, pendingAdsResult, pendingNewAdsResult, pendingEditedAdsResult, categoriesResult, subcategoriesResult, recentSignupsResult, recentAdsResult, shopsResult, pendingShopsResult, approvedShopsResult, featuredAdsResult, soldAdsResult, expiredAdsResult, totalViewsResult] = await Promise.all([\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"status\", \"active\"),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"status\", \"pending\"),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"status\", \"pending\").eq(\"is_edited\", false),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"status\", \"pending\").eq(\"is_edited\", true),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.CATEGORIES).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SUBCATEGORIES).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).gte(\"created_at\", new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).gte(\"created_at\", new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"status\", \"pending\"),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"status\", \"approved\"),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"featured\", true),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"status\", \"sold\"),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"status\", \"expired\"),\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"views\").then((result)=>{\n                if (result.error) return {\n                    sum: 0\n                };\n                return {\n                    sum: result.data?.reduce((sum, ad)=>sum + (ad.views || 0), 0) || 0\n                };\n            })\n        ]);\n        return {\n            totalUsers: usersResult.count || 0,\n            totalAds: adsResult.count || 0,\n            activeAds: activeAdsResult.count || 0,\n            pendingAds: pendingAdsResult.count || 0,\n            pendingNewAds: pendingNewAdsResult.count || 0,\n            pendingEditedAds: pendingEditedAdsResult.count || 0,\n            totalCategories: categoriesResult.count || 0,\n            totalSubcategories: subcategoriesResult.count || 0,\n            recentSignups: recentSignupsResult.count || 0,\n            recentAds: recentAdsResult.count || 0,\n            totalShops: shopsResult.count || 0,\n            pendingShops: pendingShopsResult.count || 0,\n            approvedShops: approvedShopsResult.count || 0,\n            featuredAds: featuredAdsResult.count || 0,\n            soldAds: soldAdsResult.count || 0,\n            expiredAds: expiredAdsResult.count || 0,\n            totalViews: totalViewsResult.sum || 0\n        };\n    }\n    /**\n   * Get detailed analytics data for charts and visualizations\n   */ static async getAnalyticsData(timeRange = \"7d\") {\n        const days = timeRange === \"7d\" ? 7 : timeRange === \"30d\" ? 30 : 90;\n        const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);\n        // Get user growth data\n        const userGrowthData = await this.getUserGrowthData(startDate, days);\n        // Get ad activity data\n        const adActivityData = await this.getAdActivityData(startDate, days);\n        // Get category statistics\n        const categoryStats = await this.getCategoryStats();\n        // Get location statistics\n        const locationStats = await this.getDashboardLocationStats();\n        // Get recent activity\n        const recentActivity = await this.getRecentActivity();\n        return {\n            userGrowth: userGrowthData,\n            adActivity: adActivityData,\n            categoryStats,\n            locationStats,\n            recentActivity\n        };\n    }\n    /**\n   * Get user growth data for chart\n   */ static async getUserGrowthData(startDate, days) {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"created_at\").gte(\"created_at\", startDate.toISOString()).order(\"created_at\", {\n            ascending: true\n        });\n        if (error) return [];\n        // Group by date\n        const groupedData = {};\n        const dateArray = [];\n        // Initialize all dates with 0\n        for(let i = 0; i < days; i++){\n            const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);\n            const dateStr = date.toISOString().split(\"T\")[0];\n            groupedData[dateStr] = 0;\n            dateArray.push(dateStr);\n        }\n        // Count users per date\n        data?.forEach((user)=>{\n            const dateStr = user.created_at.split(\"T\")[0];\n            if (groupedData[dateStr] !== undefined) {\n                groupedData[dateStr]++;\n            }\n        });\n        return dateArray.map((date)=>({\n                date,\n                users: groupedData[date]\n            }));\n    }\n    /**\n   * Get ad activity data for chart\n   */ static async getAdActivityData(startDate, days) {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"created_at, views\").gte(\"created_at\", startDate.toISOString()).order(\"created_at\", {\n            ascending: true\n        });\n        if (error) return [];\n        // Group by date\n        const groupedData = {};\n        const dateArray = [];\n        // Initialize all dates with 0\n        for(let i = 0; i < days; i++){\n            const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);\n            const dateStr = date.toISOString().split(\"T\")[0];\n            groupedData[dateStr] = {\n                ads: 0,\n                views: 0\n            };\n            dateArray.push(dateStr);\n        }\n        // Count ads and views per date\n        data?.forEach((ad)=>{\n            const dateStr = ad.created_at.split(\"T\")[0];\n            if (groupedData[dateStr] !== undefined) {\n                groupedData[dateStr].ads++;\n                groupedData[dateStr].views += ad.views || 0;\n            }\n        });\n        return dateArray.map((date)=>({\n                date,\n                ads: groupedData[date].ads,\n                views: groupedData[date].views\n            }));\n    }\n    /**\n   * Get category statistics\n   */ static async getCategoryStats() {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(`\n        category_id,\n        category:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.CATEGORIES}(name)\n      `);\n        if (error) return [];\n        // Count ads per category\n        const categoryCount = {};\n        let totalAds = 0;\n        data?.forEach((ad)=>{\n            if (ad.category?.name) {\n                const categoryName = ad.category.name;\n                if (!categoryCount[categoryName]) {\n                    categoryCount[categoryName] = {\n                        name: categoryName,\n                        count: 0\n                    };\n                }\n                categoryCount[categoryName].count++;\n                totalAds++;\n            }\n        });\n        return Object.values(categoryCount).map((cat)=>({\n                name: cat.name,\n                count: cat.count,\n                percentage: totalAds > 0 ? Math.round(cat.count / totalAds * 100) : 0\n            })).sort((a, b)=>b.count - a.count).slice(0, 10) // Top 10 categories\n        ;\n    }\n    /**\n   * Get location statistics for dashboard\n   */ static async getDashboardLocationStats() {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"location\").not(\"location\", \"is\", null);\n        if (error) return [];\n        // Count ads per location\n        const locationCount = {};\n        data?.forEach((ad)=>{\n            if (ad.location) {\n                locationCount[ad.location] = (locationCount[ad.location] || 0) + 1;\n            }\n        });\n        return Object.entries(locationCount).map(([location, count])=>({\n                location,\n                count\n            })).sort((a, b)=>b.count - a.count).slice(0, 10) // Top 10 locations\n        ;\n    }\n    /**\n   * Get recent activity\n   */ static async getRecentActivity() {\n        const activities = [];\n        // Get recent user signups\n        const { data: recentUsers } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"id, full_name, created_at\").order(\"created_at\", {\n            ascending: false\n        }).limit(5);\n        recentUsers?.forEach((user)=>{\n            activities.push({\n                id: `user-${user.id}`,\n                type: \"user_signup\",\n                description: `${user.full_name || \"New user\"} registered`,\n                timestamp: user.created_at,\n                user: user.full_name\n            });\n        });\n        // Get recent ads\n        const { data: recentAds } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(`\n        id, title, created_at, status,\n        user:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS}(full_name),\n        category:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.CATEGORIES}(name)\n      `).order(\"created_at\", {\n            ascending: false\n        }).limit(5);\n        recentAds?.forEach((ad)=>{\n            activities.push({\n                id: `ad-${ad.id}`,\n                type: ad.status === \"active\" ? \"ad_approved\" : \"ad_posted\",\n                description: `Ad \"${ad.title}\" posted in ${ad.category?.name || \"Unknown\"}`,\n                timestamp: ad.created_at,\n                user: ad.user?.full_name\n            });\n        });\n        // Sort by timestamp and return latest 10\n        return activities.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 10);\n    }\n    /**\n   * Get all users with admin info including wallet balances\n   */ static async getAllUsers(page = 1, limit = 20, searchQuery, roleFilter) {\n        const offset = (page - 1) * limit;\n        // Build the query with search and filters\n        let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(`\n        id, user_id, email, full_name, phone, location, gender, religion, avatar_url, is_verified,\n        role, is_super_admin, banned_until, user_type, referral_code,\n        referred_by_id, referral_level, referral_path, direct_referrals_count,\n        total_downline_count, total_commission_earned, is_referral_active,\n        created_at, updated_at\n      `, {\n            count: \"exact\"\n        }).order(\"created_at\", {\n            ascending: false\n        });\n        // Apply search filter if provided\n        if (searchQuery && searchQuery.trim()) {\n            // Check if search query is a number (OKDOI ID search)\n            const isNumeric = /^\\d+$/.test(searchQuery.trim());\n            if (isNumeric) {\n                query = query.eq(\"user_id\", parseInt(searchQuery.trim()));\n            } else {\n                query = query.or(`full_name.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%`);\n            }\n        }\n        // Apply role filter if provided\n        if (roleFilter && roleFilter !== \"all\") {\n            query = query.eq(\"role\", roleFilter);\n        }\n        // Apply pagination\n        const usersResult = await query.range(offset, offset + limit - 1);\n        if (usersResult.error) {\n            throw new Error(`Failed to fetch users: ${usersResult.error.message}`);\n        }\n        const users = usersResult.data || [];\n        // Get wallet balances for these users\n        if (users.length > 0) {\n            const userIds = users.map((user)=>user.id);\n            const { data: walletData } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"user_wallets\").select(\"user_id, balance\").in(\"user_id\", userIds);\n            // Create a map of user_id to balance for quick lookup\n            const walletMap = new Map();\n            if (walletData) {\n                walletData.forEach((wallet)=>{\n                    walletMap.set(wallet.user_id, parseFloat(wallet.balance) || 0);\n                });\n            }\n            // Add wallet balance to each user\n            users.forEach((user)=>{\n                user.wallet_balance = walletMap.get(user.id) || 0;\n            });\n        }\n        return {\n            users,\n            total: usersResult.count || 0\n        };\n    }\n    /**\n   * Get all ads for admin management with advanced filtering\n   */ static async getAllAds(filters = {}, page = 1, limit = 20) {\n        const offset = (page - 1) * limit;\n        let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(`\n        *,\n        category:categories!ads_category_id_fkey(*),\n        subcategory:subcategories!ads_subcategory_id_fkey(*),\n        user:users!ads_user_id_fkey(id, full_name, phone, email),\n        ad_images(*)\n      `).order(\"updated_at\", {\n            ascending: false\n        });\n        let countQuery = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"*\", {\n            count: \"exact\",\n            head: true\n        });\n        // Apply filters\n        if (filters.status) {\n            query = query.eq(\"status\", filters.status);\n            countQuery = countQuery.eq(\"status\", filters.status);\n        }\n        if (filters.is_edited !== undefined) {\n            query = query.eq(\"is_edited\", filters.is_edited);\n            countQuery = countQuery.eq(\"is_edited\", filters.is_edited);\n        }\n        if (filters.category_id) {\n            query = query.eq(\"category_id\", filters.category_id);\n            countQuery = countQuery.eq(\"category_id\", filters.category_id);\n        }\n        if (filters.location) {\n            query = query.ilike(\"location\", `%${filters.location}%`);\n            countQuery = countQuery.ilike(\"location\", `%${filters.location}%`);\n        }\n        if (filters.date_from) {\n            query = query.gte(\"created_at\", filters.date_from);\n            countQuery = countQuery.gte(\"created_at\", filters.date_from);\n        }\n        if (filters.date_to) {\n            query = query.lte(\"created_at\", filters.date_to);\n            countQuery = countQuery.lte(\"created_at\", filters.date_to);\n        }\n        if (filters.featured !== undefined) {\n            query = query.eq(\"featured\", filters.featured);\n            countQuery = countQuery.eq(\"featured\", filters.featured);\n        }\n        if (filters.search) {\n            const searchTerm = `%${filters.search}%`;\n            query = query.or(`title.ilike.${searchTerm},description.ilike.${searchTerm}`);\n            countQuery = countQuery.or(`title.ilike.${searchTerm},description.ilike.${searchTerm}`);\n        }\n        const [dataResult, countResult] = await Promise.all([\n            query.range(offset, offset + limit - 1),\n            countQuery\n        ]);\n        if (dataResult.error) {\n            throw new Error(`Failed to fetch ads: ${dataResult.error.message}`);\n        }\n        return {\n            ads: dataResult.data || [],\n            total: countResult.count || 0\n        };\n    }\n    /**\n   * Update ad status (approve/reject)\n   */ static async updateAdStatus(adId, status) {\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).update({\n            status\n        }).eq(\"id\", adId);\n        if (error) {\n            throw new Error(`Failed to update ad status: ${error.message}`);\n        }\n    }\n    /**\n   * Delete ad\n   */ static async deleteAd(adId) {\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).delete().eq(\"id\", adId);\n        if (error) {\n            throw new Error(`Failed to delete ad: ${error.message}`);\n        }\n    }\n    // ===== CATEGORY MANAGEMENT =====\n    /**\n   * Get all categories with subcategories and ad counts for admin\n   */ static async getAllCategoriesForAdmin() {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.CATEGORIES).select(`\n        *,\n        subcategories:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SUBCATEGORIES}(*),\n        ad_count:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS}(count)\n      `).order(\"sort_order\", {\n            ascending: true\n        });\n        if (error) {\n            throw new Error(`Failed to fetch categories: ${error.message}`);\n        }\n        return data || [];\n    }\n    /**\n   * Create a new category\n   */ static async createCategory(categoryData) {\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.CATEGORIES).insert(categoryData);\n        if (error) {\n            throw new Error(`Failed to create category: ${error.message}`);\n        }\n    }\n    /**\n   * Update a category\n   */ static async updateCategory(categoryId, categoryData) {\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.CATEGORIES).update({\n            ...categoryData,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", categoryId);\n        if (error) {\n            throw new Error(`Failed to update category: ${error.message}`);\n        }\n    }\n    /**\n   * Delete a category (with cascade handling)\n   */ static async deleteCategory(categoryId) {\n        // First check if there are any ads in this category\n        const { data: adsCount, error: countError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"id\", {\n            count: \"exact\",\n            head: true\n        }).eq(\"category_id\", categoryId);\n        if (countError) {\n            throw new Error(`Failed to check category usage: ${countError.message}`);\n        }\n        if (adsCount && adsCount > 0) {\n            throw new Error(`Cannot delete category: ${adsCount} ads are using this category`);\n        }\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.CATEGORIES).delete().eq(\"id\", categoryId);\n        if (error) {\n            throw new Error(`Failed to delete category: ${error.message}`);\n        }\n    }\n    /**\n   * Create a new subcategory\n   */ static async createSubcategory(subcategoryData) {\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SUBCATEGORIES).insert(subcategoryData);\n        if (error) {\n            throw new Error(`Failed to create subcategory: ${error.message}`);\n        }\n    }\n    /**\n   * Update a subcategory\n   */ static async updateSubcategory(subcategoryId, subcategoryData) {\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SUBCATEGORIES).update({\n            ...subcategoryData,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", subcategoryId);\n        if (error) {\n            throw new Error(`Failed to update subcategory: ${error.message}`);\n        }\n    }\n    /**\n   * Delete a subcategory (with cascade handling)\n   */ static async deleteSubcategory(subcategoryId) {\n        // First check if there are any ads in this subcategory\n        const { data: adsCount, error: countError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"id\", {\n            count: \"exact\",\n            head: true\n        }).eq(\"subcategory_id\", subcategoryId);\n        if (countError) {\n            throw new Error(`Failed to check subcategory usage: ${countError.message}`);\n        }\n        if (adsCount && adsCount > 0) {\n            throw new Error(`Cannot delete subcategory: ${adsCount} ads are using this subcategory`);\n        }\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SUBCATEGORIES).delete().eq(\"id\", subcategoryId);\n        if (error) {\n            throw new Error(`Failed to delete subcategory: ${error.message}`);\n        }\n    }\n    // ===== LOCATION MANAGEMENT =====\n    /**\n   * Get location statistics (ad counts by location)\n   */ static async getLocationStats() {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"location\").not(\"location\", \"is\", null);\n        if (error) {\n            throw new Error(`Failed to fetch location stats: ${error.message}`);\n        }\n        // Count ads by location\n        const locationCounts = {};\n        data?.forEach((ad)=>{\n            if (ad.location) {\n                locationCounts[ad.location] = (locationCounts[ad.location] || 0) + 1;\n            }\n        });\n        return Object.entries(locationCounts).map(([location, count])=>({\n                location,\n                count\n            })).sort((a, b)=>b.count - a.count);\n    }\n    /**\n   * Get all unique locations from ads\n   */ static async getAllLocationsFromAds() {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"location\").not(\"location\", \"is\", null);\n        if (error) {\n            throw new Error(`Failed to fetch locations: ${error.message}`);\n        }\n        const uniqueLocations = [\n            ...new Set(data?.map((ad)=>ad.location).filter(Boolean))\n        ];\n        return uniqueLocations.sort();\n    }\n    /**\n   * Update ads location (bulk update for location management)\n   */ static async updateAdsLocation(oldLocation, newLocation) {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).update({\n            location: newLocation\n        }).eq(\"location\", oldLocation).select(\"id\");\n        if (error) {\n            throw new Error(`Failed to update ads location: ${error.message}`);\n        }\n        return data?.length || 0;\n    }\n    /**\n   * Get category statistics with ad counts\n   */ static async getCategoryStats() {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.CATEGORIES).select(`\n        id,\n        name,\n        subcategories:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SUBCATEGORIES}(\n          id,\n          name\n        )\n      `).order(\"sort_order\", {\n            ascending: true\n        });\n        if (error) {\n            throw new Error(`Failed to fetch category stats: ${error.message}`);\n        }\n        // Get ad counts for each category and subcategory\n        const categoriesWithCounts = await Promise.all((data || []).map(async (category)=>{\n            // Get category ad count\n            const { count: categoryCount } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"id\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"category_id\", category.id);\n            // Get subcategory ad counts\n            const subcategoriesWithCounts = await Promise.all((category.subcategories || []).map(async (subcategory)=>{\n                const { count: subcategoryCount } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).select(\"id\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"subcategory_id\", subcategory.id);\n                return {\n                    id: subcategory.id,\n                    name: subcategory.name,\n                    count: subcategoryCount || 0\n                };\n            }));\n            return {\n                id: category.id,\n                name: category.name,\n                count: categoryCount || 0,\n                subcategories: subcategoriesWithCounts\n            };\n        }));\n        return categoriesWithCounts;\n    }\n    /**\n   * Toggle ad featured status\n   */ static async toggleAdFeatured(adId, featured) {\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ADS).update({\n            featured\n        }).eq(\"id\", adId);\n        if (error) {\n            throw new Error(`Failed to update featured status: ${error.message}`);\n        }\n    }\n    /**\n   * Update user role\n   */ static async updateUserRole(userId, role) {\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n            role\n        }).eq(\"id\", userId);\n        if (error) {\n            throw new Error(`Failed to update user role: ${error.message}`);\n        }\n    }\n    /**\n   * Ban/unban user\n   */ static async banUser(userId, banUntil) {\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n            banned_until: banUntil?.toISOString() || null\n        }).eq(\"id\", userId);\n        if (error) {\n            throw new Error(`Failed to ban user: ${error.message}`);\n        }\n    }\n    /**\n   * Get all vendor shops for admin management with comprehensive error handling\n   */ static async getAllShops(status, page = 1, limit = 20) {\n        try {\n            const offset = (page - 1) * limit;\n            console.log(`Fetching shops for admin - status: ${status || \"all\"}, page: ${page}, limit: ${limit}`);\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(`\n          *,\n          user:users!vendor_shops_user_id_fkey(id, full_name, phone, email, avatar_url)\n        `).order(\"created_at\", {\n                ascending: false\n            });\n            let countQuery = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            });\n            if (status && status !== \"all\") {\n                query = query.eq(\"status\", status);\n                countQuery = countQuery.eq(\"status\", status);\n            }\n            const [dataResult, countResult] = await Promise.all([\n                query.range(offset, offset + limit - 1),\n                countQuery\n            ]);\n            if (dataResult.error) {\n                console.error(\"Error fetching shops:\", dataResult.error);\n                throw new Error(`Failed to fetch shops: ${dataResult.error.message}`);\n            }\n            if (countResult.error) {\n                console.error(\"Error fetching shops count:\", countResult.error);\n                throw new Error(`Failed to fetch shops count: ${countResult.error.message}`);\n            }\n            console.log(`Successfully fetched ${dataResult.data?.length || 0} shops (total: ${countResult.count || 0})`);\n            return {\n                shops: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"AdminService.getAllShops error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Update shop status (approve/reject/suspend) with comprehensive validation\n   */ static async updateShopStatus(shopId, status, adminNotes) {\n        try {\n            if (!shopId) {\n                throw new Error(\"Shop ID is required\");\n            }\n            if (!status) {\n                throw new Error(\"Status is required\");\n            }\n            console.log(`Updating shop ${shopId} status to: ${status}`);\n            // Refresh session to ensure we have a valid token\n            const { data: { session }, error: sessionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (sessionError) {\n                console.error(\"Session error:\", sessionError);\n                throw new Error(`Session error: ${sessionError.message}`);\n            }\n            if (!session?.user) {\n                throw new Error(\"No active session. Please sign in again.\");\n            }\n            const user = session.user;\n            console.log(`Authenticated user: ${user.email} (${user.id})`);\n            // Verify admin status using the is_admin function\n            const { data: adminCheck, error: adminError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"is_admin\", {\n                user_id: user.id\n            });\n            if (adminError) {\n                console.error(\"Admin check error:\", adminError);\n                throw new Error(`Admin verification failed: ${adminError.message}`);\n            }\n            if (!adminCheck) {\n                throw new Error(\"Admin privileges required. Please contact an administrator.\");\n            }\n            console.log(\"Admin privileges verified\");\n            // First verify the shop exists\n            const { data: shop, error: fetchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"id, name, status, user_id\").eq(\"id\", shopId).single();\n            if (fetchError) {\n                console.error(\"Error fetching shop:\", fetchError);\n                throw new Error(`Shop not found: ${fetchError.message}`);\n            }\n            if (!shop) {\n                throw new Error(\"Shop not found\");\n            }\n            console.log(`Found shop: ${shop.name} (current status: ${shop.status})`);\n            const updateData = {\n                status,\n                updated_at: new Date().toISOString()\n            };\n            if (adminNotes) {\n                updateData.admin_notes = adminNotes.trim();\n            }\n            if (status === \"approved\") {\n                updateData.approved_at = new Date().toISOString();\n            }\n            console.log(\"Update data:\", updateData);\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).update(updateData).eq(\"id\", shopId).select(\"id, name, status, approved_at\");\n            if (error) {\n                console.error(\"Error updating shop status:\", error);\n                console.error(\"Error details:\", {\n                    code: error.code,\n                    message: error.message,\n                    details: error.details,\n                    hint: error.hint\n                });\n                // Provide more specific error messages\n                if (error.code === \"42501\") {\n                    throw new Error(\"Permission denied. Please ensure you are signed in as an admin.\");\n                } else if (error.code === \"PGRST116\") {\n                    throw new Error(\"Shop not found or access denied.\");\n                } else {\n                    throw new Error(`Failed to update shop status: ${error.message}`);\n                }\n            }\n            if (!data || data.length === 0) {\n                throw new Error(\"Shop update failed - no data returned. Please check your permissions.\");\n            }\n            console.log(\"Shop updated successfully:\", data[0]);\n            console.log(`Shop ${shopId} status updated to ${status} successfully`);\n        } catch (error) {\n            console.error(\"AdminService.updateShopStatus error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Delete shop with comprehensive validation\n   */ static async deleteShop(shopId) {\n        try {\n            if (!shopId) {\n                throw new Error(\"Shop ID is required\");\n            }\n            console.log(`Deleting shop: ${shopId}`);\n            // First verify the shop exists\n            const { data: shop, error: fetchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).select(\"id, name, user_id\").eq(\"id\", shopId).single();\n            if (fetchError) {\n                console.error(\"Error fetching shop:\", fetchError);\n                throw new Error(`Shop not found: ${fetchError.message}`);\n            }\n            if (!shop) {\n                throw new Error(\"Shop not found\");\n            }\n            // Delete the shop (related products, reviews, followers will be cascade deleted)\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).delete().eq(\"id\", shopId);\n            if (error) {\n                console.error(\"Error deleting shop:\", error);\n                throw new Error(`Failed to delete shop: ${error.message}`);\n            }\n            console.log(`Shop ${shopId} deleted successfully`);\n        } catch (error) {\n            console.error(\"AdminService.deleteShop error:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Toggle shop featured status\n   */ static async toggleShopFeatured(shopId, isFeatured) {\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS).update({\n            is_featured: isFeatured\n        }).eq(\"id\", shopId);\n        if (error) {\n            throw new Error(`Failed to update shop featured status: ${error.message}`);\n        }\n    }\n    /**\n   * Boost an ad manually (admin only)\n   */ static async boostAd(adId, days) {\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"admin_boost_ad\", {\n            p_ad_id: adId,\n            p_days: days\n        });\n        if (error) {\n            throw new Error(`Failed to boost ad: ${error.message}`);\n        }\n    }\n    /**\n   * Expire boost manually (admin only)\n   */ static async expireBoost(adId) {\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"admin_expire_boost\", {\n            p_ad_id: adId\n        });\n        if (error) {\n            throw new Error(`Failed to expire boost: ${error.message}`);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/admin.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Environment variables with validation\nconst supabaseUrl = \"https://vnmydqbwjjufnxngpnqo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Validate required environment variables\nif (!supabaseUrl) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n}\nif (!supabaseAnonKey) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\nif (!supabaseServiceRoleKey) {\n    console.warn(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable - admin functions will not work\");\n}\n// Create browser client with error handling\nlet supabase;\ntry {\n    supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            get (name) {\n                if (typeof document !== \"undefined\") {\n                    const value = document.cookie.split(\"; \").find((row)=>row.startsWith(`${name}=`))?.split(\"=\")[1];\n                    return value ? decodeURIComponent(value) : undefined;\n                }\n                return undefined;\n            },\n            set (name, value, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=${encodeURIComponent(value)}`;\n                    if (options?.maxAge) cookieString += `; max-age=${options.maxAge}`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    if (options?.secure) cookieString += \"; secure\";\n                    if (options?.httpOnly) cookieString += \"; httponly\";\n                    if (options?.sameSite) cookieString += `; samesite=${options.sameSite}`;\n                    document.cookie = cookieString;\n                }\n            },\n            remove (name, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    document.cookie = cookieString;\n                }\n            }\n        }\n    });\n} catch (error) {\n    console.error(\"Failed to create Supabase browser client:\", error);\n    // Fallback to basic client without SSR\n    supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Admin client with service role key for bypassing RLS\n// Note: This will be null on client-side for security reasons\nconst supabaseAdmin = supabaseServiceRoleKey ? (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceRoleKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n\n// Database table names\nconst TABLES = {\n    CATEGORIES: \"categories\",\n    SUBCATEGORIES: \"subcategories\",\n    ADS: \"ads\",\n    USERS: \"users\",\n    AD_IMAGES: \"ad_images\",\n    DISTRICTS: \"districts\",\n    CITIES: \"cities\",\n    USER_FAVORITES: \"user_favorites\",\n    VENDOR_SHOPS: \"vendor_shops\",\n    SHOP_PRODUCTS: \"shop_products\",\n    SHOP_PRODUCT_IMAGES: \"shop_product_images\",\n    SHOP_REVIEWS: \"shop_reviews\",\n    SHOP_FOLLOWERS: \"shop_followers\",\n    SHOP_CATEGORIES: \"shop_categories\",\n    SHOP_SUBCATEGORIES: \"shop_subcategories\",\n    PRODUCT_REVIEWS: \"product_reviews\",\n    CHAT_CONVERSATIONS: \"chat_conversations\",\n    CHAT_MESSAGES: \"chat_messages\",\n    USER_WALLETS: \"user_wallets\",\n    WALLET_TRANSACTIONS: \"wallet_transactions\",\n    P2P_TRANSFERS: \"p2p_transfers\",\n    DEPOSIT_REQUESTS: \"deposit_requests\",\n    WITHDRAWAL_REQUESTS: \"withdrawal_requests\",\n    SUBSCRIPTION_PACKAGES: \"subscription_packages\",\n    USER_SUBSCRIPTIONS: \"user_subscriptions\",\n    AD_BOOSTS: \"ad_boosts\",\n    BOOST_PACKAGES: \"boost_packages\",\n    // Order Management System\n    CART_ITEMS: \"cart_items\",\n    SHOP_ORDERS: \"shop_orders\",\n    ORDER_ITEMS: \"order_items\",\n    ORDER_STATUS_HISTORY: \"order_status_history\",\n    // Merchant Wallet System\n    MERCHANT_WALLETS: \"merchant_wallets\",\n    MERCHANT_WALLET_TRANSACTIONS: \"merchant_wallet_transactions\",\n    MERCHANT_TO_MAIN_TRANSFERS: \"merchant_to_main_transfers\",\n    // Referral & Commission System\n    REFERRAL_HIERARCHY: \"referral_hierarchy\",\n    COMMISSION_STRUCTURE: \"commission_structure\",\n    COMMISSION_TRANSACTIONS: \"commission_transactions\",\n    REFERRAL_PLACEMENTS: \"referral_placements\",\n    // KYC System\n    KYC_SUBMISSIONS: \"kyc_submissions\",\n    KYC_STATUS_HISTORY: \"kyc_status_history\",\n    KYC_DOCUMENT_TYPES: \"kyc_document_types\",\n    ZONAL_MANAGERS: \"zonal_managers\",\n    REGIONAL_SALES_MANAGERS: \"regional_sales_managers\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcompany-wallet%2Froute&page=%2Fapi%2Fadmin%2Fcompany-wallet%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcompany-wallet%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();