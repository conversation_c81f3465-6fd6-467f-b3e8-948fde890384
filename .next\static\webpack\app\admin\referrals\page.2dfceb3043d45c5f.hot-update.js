"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/referrals/page",{

/***/ "(app-pages-browser)/./src/app/admin/referrals/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin/referrals/page.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ReferralSystemPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Eye,Network,Save,Search,TrendingUp,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Eye,Network,Save,Search,TrendingUp,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Eye,Network,Save,Search,TrendingUp,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Eye,Network,Save,Search,TrendingUp,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Eye,Network,Save,Search,TrendingUp,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Eye,Network,Save,Search,TrendingUp,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Eye,Network,Save,Search,TrendingUp,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Eye,Network,Save,Search,TrendingUp,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Eye,Network,Save,Search,TrendingUp,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/referralSystem */ \"(app-pages-browser)/./src/lib/services/referralSystem.ts\");\n/* harmony import */ var _lib_services_commissionSystem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/commissionSystem */ \"(app-pages-browser)/./src/lib/services/commissionSystem.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Lazy load the heavy referral tree component\nconst EnhancedReferralTree = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_admin_EnhancedReferralTree_tsx-_d5ef0\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/admin/EnhancedReferralTree */ \"(app-pages-browser)/./src/components/admin/EnhancedReferralTree.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\admin\\\\referrals\\\\page.tsx -> \" + \"@/components/admin/EnhancedReferralTree\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-96\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, undefined),\n    ssr: false // This component is heavy and not needed for SEO\n});\n_c = EnhancedReferralTree;\n\n\nfunction ReferralSystemPage() {\n    var _zonalManagers_find;\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalUsers: 0,\n        zonalManagers: 0,\n        regionalManagers: 0,\n        totalCommissions: 0,\n        pendingCommissions: 0\n    });\n    const [zonalManagers, setZonalManagers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [regionalManagers, setRegionalManagers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showCreateZM, setShowCreateZM] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUpgradeRSM, setShowUpgradeRSM] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAssignOKDOIHead, setShowAssignOKDOIHead] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNetworkTree, setShowNetworkTree] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchUsers, setSearchUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userSearchTerm, setUserSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [networkTree, setNetworkTree] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [networkTreeLoading, setNetworkTreeLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form states\n    const [zmForm, setZmForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        userId: \"\",\n        zoneName: \"\",\n        zoneDescription: \"\",\n        assignedDistricts: []\n    });\n    const [rsmForm, setRsmForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        userId: \"\",\n        zonalManagerId: \"\",\n        regionName: \"\"\n    });\n    const [eligibleUsersForRSM, setEligibleUsersForRSM] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedZMForRSM, setSelectedZMForRSM] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loadingEligibleUsers, setLoadingEligibleUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userSearchResults, setUserSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [okdoiHead, setOkdoiHead] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n        checkOKDOIHead();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timeoutId = setTimeout(()=>{\n            searchUsersForAssignment(userSearchTerm);\n        }, 300);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        userSearchTerm\n    ]);\n    const checkOKDOIHead = async ()=>{\n        try {\n            const response = await fetch(\"/api/admin/create-okdoi-head\", {\n                method: \"GET\"\n            });\n            if (response.ok) {\n                const result = await response.json();\n                setOkdoiHead(result.data);\n            }\n        } catch (error) {\n            console.error(\"Error checking OKDOI Head:\", error);\n        }\n    };\n    const createOKDOIHead = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/admin/create-okdoi-head\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || \"Failed to create OKDOI Head\");\n            }\n            setOkdoiHead(result.data);\n            setSuccess(\"OKDOI Head created successfully\");\n            setTimeout(()=>setSuccess(\"\"), 3000);\n        } catch (error) {\n            console.error(\"Error creating OKDOI Head:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to create OKDOI Head\");\n            setTimeout(()=>setError(\"\"), 3000);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const assignOKDOIHeadToUser = async ()=>{\n        if (!selectedUser) {\n            setError(\"Please select a user\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/admin/create-okdoi-head\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: selectedUser.id\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || \"Failed to assign OKDOI Head\");\n            }\n            setOkdoiHead(result.data);\n            setSuccess(\"OKDOI Head assigned successfully\");\n            setShowAssignOKDOIHead(false);\n            setSelectedUser(null);\n            setUserSearchTerm(\"\");\n            setSearchUsers([]);\n            setTimeout(()=>setSuccess(\"\"), 3000);\n        } catch (error) {\n            console.error(\"Error assigning OKDOI Head:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to assign OKDOI Head\");\n            setTimeout(()=>setError(\"\"), 3000);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const searchUsersForAssignment = async (query)=>{\n        if (!query.trim()) {\n            setSearchUsers([]);\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/admin/search-users?q=\".concat(encodeURIComponent(query), \"&limit=10\"));\n            const result = await response.json();\n            if (response.ok) {\n                setSearchUsers(result.data || []);\n            }\n        } catch (error) {\n            console.error(\"Error searching users:\", error);\n        }\n    };\n    const loadNetworkTree = async function() {\n        let maxDepth = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 999;\n        try {\n            setNetworkTreeLoading(true);\n            // Use unlimited depth to show all users in the tree\n            const response = await fetch(\"/api/admin/network-tree?maxDepth=\".concat(maxDepth, \"&lazy=true\"));\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || \"Failed to load network tree\");\n            }\n            setNetworkTree(result.data);\n        } catch (error) {\n            console.error(\"Error loading network tree:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to load network tree\");\n            setTimeout(()=>setError(\"\"), 3000);\n        } finally{\n            setNetworkTreeLoading(false);\n        }\n    };\n    const handleViewNetworkTree = ()=>{\n        setShowNetworkTree(true);\n        if (!networkTree) {\n            loadNetworkTree();\n        }\n    };\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            // Load referral system stats\n            const referralStats = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_6__.ReferralSystemService.getReferralSystemStats();\n            // Load commission summary\n            const commissionSummary = await _lib_services_commissionSystem__WEBPACK_IMPORTED_MODULE_7__.CommissionSystemService.getCommissionSummary();\n            setStats({\n                totalUsers: referralStats.totalUsers,\n                zonalManagers: referralStats.zonalManagers,\n                regionalManagers: referralStats.regionalManagers,\n                totalCommissions: commissionSummary.totalCommissionsPaid,\n                pendingCommissions: commissionSummary.pendingCommissions\n            });\n            // Load ZMs and RSMs\n            const [zmData, rsmData] = await Promise.all([\n                _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_6__.ReferralSystemService.getZonalManagers(),\n                _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_6__.ReferralSystemService.getRegionalSalesManagers()\n            ]);\n            setZonalManagers(zmData);\n            setRegionalManagers(rsmData);\n        } catch (error) {\n            console.error(\"Error loading referral data:\", error);\n            setError(\"Failed to load referral system data\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSearchUsers = async (searchTerm)=>{\n        if (searchTerm.length < 2) {\n            setUserSearchResults([]);\n            return;\n        }\n        try {\n            const results = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_6__.ReferralSystemService.searchUsers(searchTerm);\n            setUserSearchResults(results);\n        } catch (error) {\n            console.error(\"Error searching users:\", error);\n        }\n    };\n    const handleCreateZM = async ()=>{\n        if (!zmForm.userId || !zmForm.zoneName) {\n            setError(\"Please fill in all required fields\");\n            return;\n        }\n        try {\n            setLoading(true);\n            await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_6__.ReferralSystemService.createZonalManager(zmForm.userId, zmForm.zoneName, zmForm.zoneDescription, zmForm.assignedDistricts);\n            setSuccess(\"Zonal Manager created successfully\");\n            setShowCreateZM(false);\n            setZmForm({\n                userId: \"\",\n                zoneName: \"\",\n                zoneDescription: \"\",\n                assignedDistricts: []\n            });\n            await loadData();\n        } catch (error) {\n            setError(error instanceof Error ? error.message : \"Failed to create Zonal Manager\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleZMSelectionForRSM = async (zmId)=>{\n        try {\n            setSelectedZMForRSM(zmId);\n            setRsmForm({\n                ...rsmForm,\n                zonalManagerId: zmId,\n                userId: \"\"\n            });\n            setError(\"\") // Clear any previous errors\n            ;\n            if (zmId && typeof zmId === \"string\" && zmId.trim() !== \"\") {\n                setLoadingEligibleUsers(true);\n                setEligibleUsersForRSM([]) // Clear previous results\n                ;\n                console.log(\"Loading eligible users for ZM:\", zmId);\n                // Call the API route to get eligible users (server-side with admin privileges)\n                const response = await fetch(\"/api/admin/referrals/eligible-users?zmId=\".concat(encodeURIComponent(zmId)), {\n                    method: \"GET\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                const result = await response.json();\n                console.log(\"API response:\", result);\n                if (result.success && Array.isArray(result.data)) {\n                    // Filter out any invalid users as an extra safety measure\n                    const validUsers = result.data.filter((user)=>user && typeof user === \"object\" && user.id && typeof user.id === \"string\");\n                    setEligibleUsersForRSM(validUsers);\n                    console.log(\"Set \".concat(validUsers.length, \" valid eligible users\"));\n                } else {\n                    console.warn(\"Invalid API response:\", result);\n                    setEligibleUsersForRSM([]);\n                    setError(result.error || \"Failed to load eligible users. Please try again.\");\n                }\n            } else {\n                setEligibleUsersForRSM([]);\n                setLoadingEligibleUsers(false);\n            }\n        } catch (error) {\n            console.error(\"Error in handleZMSelectionForRSM:\", error);\n            setEligibleUsersForRSM([]);\n            setError(error instanceof Error ? error.message : \"Failed to load eligible users. Please try again.\");\n        } finally{\n            setLoadingEligibleUsers(false);\n        }\n    };\n    const handleUpgradeRSM = async ()=>{\n        if (!rsmForm.userId) {\n            setError(\"Please select a user to upgrade\");\n            return;\n        }\n        if (!selectedZMForRSM) {\n            setError(\"Please select a Zonal Manager first\");\n            return;\n        }\n        try {\n            setLoading(true);\n            setError(\"\") // Clear any previous errors\n            ;\n            // Call the API route to upgrade user to RSM (server-side with admin privileges)\n            const response = await fetch(\"/api/admin/referrals/upgrade-rsm\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: rsmForm.userId,\n                    zonalManagerId: rsmForm.zonalManagerId || selectedZMForRSM,\n                    regionName: rsmForm.regionName || undefined,\n                    upgradedBy: null // Field is nullable, so we pass null instead of invalid UUID\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setSuccess(\"User upgraded to RSM successfully\");\n                setShowUpgradeRSM(false);\n                setRsmForm({\n                    userId: \"\",\n                    zonalManagerId: \"\",\n                    regionName: \"\"\n                });\n                setSelectedZMForRSM(\"\");\n                setEligibleUsersForRSM([]);\n                await loadData();\n            } else {\n                setError(result.error || \"Failed to upgrade user to RSM\");\n            }\n        } catch (error) {\n            console.error(\"Error upgrading user to RSM:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to upgrade user to RSM\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const StatCard = (param)=>{\n        let { title, value, icon: Icon, color } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 rounded-lg \".concat(color),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"h-6 w-6 text-white\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-600\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: typeof value === \"number\" ? value.toLocaleString() : value\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                lineNumber: 407,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n            lineNumber: 406,\n            columnNumber: 5\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                    lineNumber: 425,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                lineNumber: 424,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n            lineNumber: 423,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-primary-blue\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"Referral System\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Manage referral hierarchy and commission distribution\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                okdoiHead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    onClick: handleViewNetworkTree,\n                                    className: \"flex items-center space-x-2 bg-green-500 hover:bg-green-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"View Network Tree\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 15\n                                }, this),\n                                !okdoiHead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            onClick: createOKDOIHead,\n                                            className: \"flex items-center space-x-2 bg-amber-500 hover:bg-amber-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Create OKDOI Head\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            onClick: ()=>setShowAssignOKDOIHead(true),\n                                            className: \"flex items-center space-x-2 bg-blue-500 hover:bg-blue-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Assign to Existing User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    onClick: ()=>setShowCreateZM(true),\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Create ZM\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    onClick: ()=>setShowUpgradeRSM(true),\n                                    variant: \"outline\",\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Upgrade to RSM\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                    lineNumber: 435,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-800\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 11\n                }, this),\n                success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-800\",\n                        children: success\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                    lineNumber: 481,\n                    columnNumber: 11\n                }, this),\n                okdoiHead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-6 w-6 text-amber-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-amber-900\",\n                                                children: \"OKDOI Head Active\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-amber-700\",\n                                                children: [\n                                                    okdoiHead.full_name,\n                                                    \" (\",\n                                                    okdoiHead.email,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-amber-600\",\n                                                children: [\n                                                    \"Referral Code: \",\n                                                    okdoiHead.referral_code\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-amber-900\",\n                                        children: okdoiHead.totalReferrals || 0\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-amber-600\",\n                                        children: \"Total Network Size\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"OKDOI Head\",\n                            value: okdoiHead ? 1 : 0,\n                            icon: _barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                            color: \"bg-amber-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Total Users\",\n                            value: stats.totalUsers,\n                            icon: _barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                            color: \"bg-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Zonal Managers\",\n                            value: stats.zonalManagers,\n                            icon: _barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                            color: \"bg-purple-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Regional Managers\",\n                            value: stats.regionalManagers,\n                            icon: _barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                            color: \"bg-green-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Total Commissions\",\n                            value: \"Rs \".concat(stats.totalCommissions.toLocaleString()),\n                            icon: _barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                            color: \"bg-emerald-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                            title: \"Pending Commissions\",\n                            value: \"Rs \".concat(stats.pendingCommissions.toLocaleString()),\n                            icon: _barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                            color: \"bg-orange-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search users, ZMs, or RSMs...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 554,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 551,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                children: \"Search\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                        lineNumber: 550,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                    lineNumber: 549,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"Zonal Managers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 570,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: zonalManagers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"No zonal managers found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 576,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        onClick: ()=>setShowCreateZM(true),\n                                        className: \"mt-4\",\n                                        children: \"Create First ZM\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Zone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Referral Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: zonalManagers.map((zm)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                            children: zm.user.full_name || \"N/A\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: zm.zone_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono\",\n                                                            children: zm.user.referral_code || \"N/A\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(zm.is_active ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"),\n                                                                children: zm.is_active ? \"Active\" : \"Inactive\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                            lineNumber: 615,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{},\n                                                                    className: \"text-primary-blue hover:text-primary-blue/80 mr-3\",\n                                                                    children: \"View\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                                    lineNumber: 625,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                zm.is_active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{},\n                                                                    className: \"text-red-600 hover:text-red-800\",\n                                                                    children: \"Deactivate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                                    lineNumber: 632,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, zm.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                    lineNumber: 568,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"Regional Sales Managers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: regionalManagers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"No regional sales managers found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 658,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        onClick: ()=>setShowUpgradeRSM(true),\n                                        className: \"mt-4\",\n                                        children: \"Upgrade First RSM\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 656,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Region\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Zonal Manager\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Referral Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 666,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: regionalManagers.map((rsm)=>{\n                                                var _rsm_zonal_manager;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                            children: rsm.user.full_name || \"N/A\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: rsm.region_name || \"N/A\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                            lineNumber: 694,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: ((_rsm_zonal_manager = rsm.zonal_manager) === null || _rsm_zonal_manager === void 0 ? void 0 : _rsm_zonal_manager.zone_name) || \"N/A\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono\",\n                                                            children: rsm.user.referral_code || \"N/A\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(rsm.is_active ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"),\n                                                                children: rsm.is_active ? \"Active\" : \"Inactive\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                                lineNumber: 704,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{},\n                                                                    className: \"text-primary-blue hover:text-primary-blue/80 mr-3\",\n                                                                    children: \"View\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                                    lineNumber: 713,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                rsm.is_active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{},\n                                                                    className: \"text-red-600 hover:text-red-800\",\n                                                                    children: \"Deactivate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                                    lineNumber: 720,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                            lineNumber: 712,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, rsm.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 664,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                            lineNumber: 654,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                    lineNumber: 650,\n                    columnNumber: 9\n                }, this),\n                showCreateZM && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: \"Create Zonal Manager\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowCreateZM(false),\n                                        className: \"text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 741,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Search User\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                placeholder: \"Search by name or email...\",\n                                                value: userSearchTerm,\n                                                onChange: (e)=>{\n                                                    setUserSearchTerm(e.target.value);\n                                                    handleSearchUsers(e.target.value);\n                                                },\n                                                fullWidth: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 19\n                                            }, this),\n                                            userSearchResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 border border-gray-200 rounded-lg max-h-40 overflow-y-auto\",\n                                                children: userSearchResults.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setZmForm({\n                                                                ...zmForm,\n                                                                userId: user.id\n                                                            });\n                                                            setUserSearchTerm(user.full_name || user.email);\n                                                            setUserSearchResults([]);\n                                                        },\n                                                        className: \"w-full text-left px-3 py-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: user.full_name || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                                lineNumber: 772,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                                lineNumber: 773,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, user.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 761,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        label: \"Zone Name\",\n                                        value: zmForm.zoneName,\n                                        onChange: (e)=>setZmForm({\n                                                ...zmForm,\n                                                zoneName: e.target.value\n                                            }),\n                                        placeholder: \"Enter zone name\",\n                                        fullWidth: true,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 780,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Zone Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 790,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: zmForm.zoneDescription,\n                                                onChange: (e)=>setZmForm({\n                                                        ...zmForm,\n                                                        zoneDescription: e.target.value\n                                                    }),\n                                                placeholder: \"Enter zone description (optional)\",\n                                                rows: 3,\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 791,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 789,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 748,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-3 mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        variant: \"outline\",\n                                        onClick: ()=>setShowCreateZM(false),\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 802,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        onClick: handleCreateZM,\n                                        loading: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 806,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Create ZM\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 805,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 801,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                        lineNumber: 740,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                    lineNumber: 739,\n                    columnNumber: 11\n                }, this),\n                showUpgradeRSM && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: \"Upgrade to RSM\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 819,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowUpgradeRSM(false);\n                                            setSelectedZMForRSM(\"\");\n                                            setEligibleUsersForRSM([]);\n                                            setRsmForm({\n                                                userId: \"\",\n                                                zonalManagerId: \"\",\n                                                regionName: \"\"\n                                            });\n                                        },\n                                        className: \"text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 829,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 820,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 818,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: [\n                                                    \"Step 1: Select Zonal Manager \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 837,\n                                                        columnNumber: 50\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedZMForRSM,\n                                                onChange: (e)=>handleZMSelectionForRSM(e.target.value),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select Zonal Manager\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 844,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    zonalManagers.filter((zm)=>zm.is_active).map((zm)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: zm.id,\n                                                            children: [\n                                                                zm.zone_name,\n                                                                \" - \",\n                                                                zm.user.full_name\n                                                            ]\n                                                        }, zm.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                            lineNumber: 846,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 839,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-1\",\n                                                children: \"Each ZM can have only 1 RSM for each direct downline. Select a ZM to see eligible users.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 851,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 835,\n                                        columnNumber: 17\n                                    }, this),\n                                    selectedZMForRSM && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: [\n                                                    \"Step 2: Select User from \",\n                                                    (_zonalManagers_find = zonalManagers.find((zm)=>zm.id === selectedZMForRSM)) === null || _zonalManagers_find === void 0 ? void 0 : _zonalManagers_find.zone_name,\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 860,\n                                                        columnNumber: 114\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 859,\n                                                columnNumber: 21\n                                            }, this),\n                                            loadingEligibleUsers ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary-blue mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                            lineNumber: 865,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-blue-800\",\n                                                            children: \"Loading eligible users from the entire network...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                            lineNumber: 866,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                    lineNumber: 864,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 863,\n                                                columnNumber: 23\n                                            }, this) : eligibleUsersForRSM.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: rsmForm.userId,\n                                                onChange: (e)=>setRsmForm({\n                                                        ...rsmForm,\n                                                        userId: e.target.value\n                                                    }),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select User to Upgrade\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 875,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    eligibleUsersForRSM.filter((user)=>user && user.id) // Extra safety filter\n                                                    .map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: user.id,\n                                                            children: [\n                                                                user.full_name || \"N/A\",\n                                                                \" - \",\n                                                                user.email || \"N/A\"\n                                                            ]\n                                                        }, user.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                            lineNumber: 879,\n                                                            columnNumber: 29\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 870,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-yellow-800\",\n                                                    children: \"No eligible users found in this ZM's network. Users must not already be RSMs and each direct line can only have 1 RSM.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 885,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 858,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        label: \"Region Name (Optional)\",\n                                        value: rsmForm.regionName,\n                                        onChange: (e)=>setRsmForm({\n                                                ...rsmForm,\n                                                regionName: e.target.value\n                                            }),\n                                        placeholder: \"Enter region name\",\n                                        fullWidth: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 894,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 833,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-3 mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        variant: \"outline\",\n                                        onClick: ()=>{\n                                            setShowUpgradeRSM(false);\n                                            setSelectedZMForRSM(\"\");\n                                            setEligibleUsersForRSM([]);\n                                            setRsmForm({\n                                                userId: \"\",\n                                                zonalManagerId: \"\",\n                                                regionName: \"\"\n                                            });\n                                        },\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        onClick: handleUpgradeRSM,\n                                        loading: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 916,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Upgrade to RSM\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 915,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 903,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                        lineNumber: 817,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                    lineNumber: 816,\n                    columnNumber: 11\n                }, this),\n                showAssignOKDOIHead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 w-full max-w-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Assign OKDOI Head\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 929,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowAssignOKDOIHead(false);\n                                            setSelectedUser(null);\n                                            setUserSearchTerm(\"\");\n                                            setSearchUsers([]);\n                                        },\n                                        className: \"text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 939,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 928,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Search User\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 945,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        value: userSearchTerm,\n                                                        onChange: (e)=>setUserSearchTerm(e.target.value),\n                                                        placeholder: \"Search by name or email...\",\n                                                        fullWidth: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 947,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    searchUsers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute z-10 w-full bg-white border border-gray-300 rounded-lg mt-1 max-h-48 overflow-y-auto shadow-lg\",\n                                                        children: searchUsers.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    setSelectedUser(user);\n                                                                    setUserSearchTerm(user.full_name || user.email);\n                                                                    setSearchUsers([]);\n                                                                },\n                                                                className: \"w-full text-left px-3 py-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: user.full_name || \"N/A\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                                        lineNumber: 965,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: user.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                                        lineNumber: 966,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: [\n                                                                            \"Role: \",\n                                                                            user.role\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                                        lineNumber: 967,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, user.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                                lineNumber: 956,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                        lineNumber: 954,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 946,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 944,\n                                        columnNumber: 17\n                                    }, this),\n                                    selectedUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 p-3 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-blue-900\",\n                                                children: \"Selected User:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 977,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-800\",\n                                                children: selectedUser.full_name || \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 978,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-600\",\n                                                children: selectedUser.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 979,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-500\",\n                                                children: [\n                                                    \"Current Role: \",\n                                                    selectedUser.role\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 976,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 943,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-3 mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        variant: \"outline\",\n                                        onClick: ()=>{\n                                            setShowAssignOKDOIHead(false);\n                                            setSelectedUser(null);\n                                            setUserSearchTerm(\"\");\n                                            setSearchUsers([]);\n                                        },\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 986,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        onClick: assignOKDOIHeadToUser,\n                                        loading: loading,\n                                        disabled: !selectedUser,\n                                        className: \"bg-amber-500 hover:bg-amber-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 1003,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Assign OKDOI Head\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 997,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 985,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                        lineNumber: 927,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                    lineNumber: 926,\n                    columnNumber: 11\n                }, this),\n                showNetworkTree && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg w-full max-w-6xl h-5/6 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-6 w-6 text-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 1017,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: \"Complete Network Tree\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                                lineNumber: 1018,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 1016,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowNetworkTree(false),\n                                        className: \"text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Eye_Network_Save_Search_TrendingUp_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 1024,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                        lineNumber: 1020,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 1015,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedReferralTree, {\n                                    rootNode: networkTree,\n                                    loading: networkTreeLoading,\n                                    onNodeClick: (user)=>{\n                                        console.log(\"Node clicked:\", user);\n                                    // You can add more functionality here like showing user details\n                                    },\n                                    className: \"h-full\",\n                                    onRefresh: loadNetworkTree,\n                                    isRefreshing: networkTreeLoading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                    lineNumber: 1029,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 1028,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Click on nodes to view details • Expand/collapse using the arrow buttons\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 1044,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            onClick: ()=>setShowNetworkTree(false),\n                                            variant: \"outline\",\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                            lineNumber: 1047,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                    lineNumber: 1043,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                                lineNumber: 1042,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                        lineNumber: 1014,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n                    lineNumber: 1013,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n            lineNumber: 433,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\referrals\\\\page.tsx\",\n        lineNumber: 432,\n        columnNumber: 5\n    }, this);\n}\n_s(ReferralSystemPage, \"qVor2FV/vZQllXnmahMfVS1E78I=\");\n_c1 = ReferralSystemPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"EnhancedReferralTree\");\n$RefreshReg$(_c1, \"ReferralSystemPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/referrals/page.tsx\n"));

/***/ })

});