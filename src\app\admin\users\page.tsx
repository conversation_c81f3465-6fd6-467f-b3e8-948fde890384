'use client'

import { useState, useEffect, useRef } from 'react'
import {
  Users,
  Search,
  Filter,
  MoreVertical,
  Shield,
  Ban,
  UserCheck,
  Calendar,
  Mail,
  Phone,
  Network,
  User,
  X,
  Award
} from 'lucide-react'
import AdminLayout from '@/components/admin/AdminLayout'
import HierarchicalNetworkTree, { NetworkTreeNode } from '@/components/admin/HierarchicalNetworkTree'
import dynamic from 'next/dynamic'
import { EnhancedNetworkTreeNode } from '@/components/admin/EnhancedReferralTree'

// Lazy load the heavy referral tree component
const EnhancedReferralTree = dynamic(
  () => import('@/components/admin/EnhancedReferralTree'),
  {
    loading: () => (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
      </div>
    ),
    ssr: false
  }
)
import ReferralRankBadge from '@/components/ui/ReferralRankBadge'
import { AdminService, AdminUser } from '@/lib/services/admin'
import ViewUserDataModal from '@/components/admin/ViewUserDataModal'
import { Key } from 'lucide-react'
import { showAlert } from '@/components/ui/ConfirmationDialog'

interface UserActionsProps {
  user: AdminUser
  onRoleUpdate: (userId: string, role: string) => void
  onBanUser: (userId: string) => void
  onViewTree: (userId: string) => void
  onViewUserData: (user: AdminUser) => void
  onUpdatePassword: (user: AdminUser) => void
}

function UserActions({ user, onRoleUpdate, onBanUser, onViewTree, onViewUserData, onUpdatePassword }: UserActionsProps) {
  const [showMenu, setShowMenu] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setShowMenu(!showMenu)}
        className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
      >
        <MoreVertical className="h-4 w-4" />
      </button>
      
      {showMenu && (
        <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-[9999]">
          <div className="py-1">
            <button
              onClick={() => {
                onViewUserData(user)
                setShowMenu(false)
              }}
              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <User className="h-4 w-4 mr-2" />
              View User Data
            </button>
            <button
              onClick={() => {
                onViewTree(user.id)
                setShowMenu(false)
              }}
              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <Network className="h-4 w-4 mr-2" />
              View Referral Tree
            </button>
            <button
              onClick={() => {
                onUpdatePassword(user)
                setShowMenu(false)
              }}
              className="flex items-center w-full px-4 py-2 text-sm text-blue-600 hover:bg-blue-50"
            >
              <Key className="h-4 w-4 mr-2" />
              Update Password
            </button>
            <button
              onClick={() => {
                onRoleUpdate(user.id, user.role === 'admin' ? 'user' : 'admin')
                setShowMenu(false)
              }}
              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <Shield className="h-4 w-4 mr-2" />
              {user.role === 'admin' ? 'Remove Admin' : 'Make Admin'}
            </button>
            <button
              onClick={() => {
                onBanUser(user.id)
                setShowMenu(false)
              }}
              className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
            >
              <Ban className="h-4 w-4 mr-2" />
              {user.banned_until ? 'Unban User' : 'Ban User'}
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default function AdminUsers() {
  const [users, setUsers] = useState<AdminUser[]>([])
  const [loading, setLoading] = useState(true)
  const [searching, setSearching] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalUsers, setTotalUsers] = useState(0)
  const [selectedRole, setSelectedRole] = useState<string>('all')
  const [showReferralTree, setShowReferralTree] = useState(false)
  const [selectedUserTree, setSelectedUserTree] = useState<EnhancedNetworkTreeNode | null>(null)
  const [treeLoading, setTreeLoading] = useState(false)
  const [selectedUserInfo, setSelectedUserInfo] = useState<AdminUser | null>(null)
  const [showUserDataModal, setShowUserDataModal] = useState(false)
  const [selectedUserData, setSelectedUserData] = useState<AdminUser | null>(null)
  const [showPasswordModal, setShowPasswordModal] = useState(false)
  const [selectedUserForPassword, setSelectedUserForPassword] = useState<AdminUser | null>(null)
  const [newPassword, setNewPassword] = useState('')
  const [updatingPassword, setUpdatingPassword] = useState(false)
  const [giftSystemData, setGiftSystemData] = useState<Map<string, number>>(new Map())

  const usersPerPage = 20

  useEffect(() => {
    fetchUsers()
  }, [currentPage, selectedRole])

  // Debounced search effect
  useEffect(() => {
    setSearching(true)
    const timeoutId = setTimeout(() => {
      if (currentPage === 1) {
        fetchUsers()
      } else {
        setCurrentPage(1) // Reset to first page when searching
      }
      setSearching(false)
    }, 500) // 500ms debounce

    return () => {
      clearTimeout(timeoutId)
      setSearching(false)
    }
  }, [searchTerm])

  const fetchGiftSystemData = async (userIds: string[]) => {
    try {
      const giftSystemMap = new Map<string, number>()

      // Fetch gift system commissions for all users in parallel
      const promises = userIds.map(async (userId) => {
        try {
          const response = await fetch(`/api/commission-breakdown?userId=${userId}&includeGiftSystem=true`)
          if (response.ok) {
            const result = await response.json()
            if (result.success && result.data.giftSystemCommissions) {
              giftSystemMap.set(userId, result.data.giftSystemCommissions.totalGiftSystem)
            }
          }
        } catch (error) {
          console.error(`Error fetching gift system data for user ${userId}:`, error)
        }
      })

      await Promise.all(promises)
      setGiftSystemData(giftSystemMap)
    } catch (error) {
      console.error('Error fetching gift system data:', error)
    }
  }

  const fetchUsers = async () => {
    try {
      setLoading(true)
      const { users: userData, total } = await AdminService.getAllUsers(
        currentPage,
        usersPerPage,
        searchTerm.trim() || undefined,
        selectedRole !== 'all' ? selectedRole : undefined
      )
      setUsers(userData)
      setTotalUsers(total)

      // Fetch gift system data for the current page of users
      if (userData.length > 0) {
        const userIds = userData.map(user => user.id)
        await fetchGiftSystemData(userIds)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load users')
    } finally {
      setLoading(false)
    }
  }

  const handleRoleUpdate = async (userId: string, newRole: string) => {
    try {
      await AdminService.updateUserRole(userId, newRole)
      await fetchUsers() // Refresh the list
    } catch (err) {
      alert('Failed to update user role')
    }
  }

  const handleBanUser = async (userId: string) => {
    try {
      const user = users.find(u => u.id === userId)
      const banUntil = user?.banned_until ? undefined : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      await AdminService.banUser(userId, banUntil)
      await fetchUsers() // Refresh the list
    } catch (err) {
      await showAlert({
        title: 'Error',
        message: 'Failed to ban/unban user',
        variant: 'danger'
      })
    }
  }

  const handleViewUserData = (user: AdminUser) => {
    setSelectedUserData(user)
    setShowUserDataModal(true)
  }

  const handleUpdatePassword = (user: AdminUser) => {
    setSelectedUserForPassword(user)
    setNewPassword('')
    setShowPasswordModal(true)
  }

  const handlePasswordUpdate = async () => {
    if (!selectedUserForPassword || !newPassword) {
      return
    }

    try {
      setUpdatingPassword(true)

      const response = await fetch('/api/admin/users/update-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: selectedUserForPassword.email,
          newPassword: newPassword
        })
      })

      const result = await response.json()

      if (result.success) {
        await showAlert({
          title: 'Success',
          message: `Password updated successfully for ${selectedUserForPassword.email}`,
          variant: 'success'
        })
        setShowPasswordModal(false)
        setSelectedUserForPassword(null)
        setNewPassword('')
      } else {
        await showAlert({
          title: 'Error',
          message: result.error || 'Failed to update password',
          variant: 'danger'
        })
      }
    } catch (error) {
      console.error('Error updating password:', error)
      await showAlert({
        title: 'Error',
        message: 'Failed to update password',
        variant: 'danger'
      })
    } finally {
      setUpdatingPassword(false)
    }
  }

  const handleViewReferralTree = async (userId: string) => {
    try {
      const user = users.find(u => u.id === userId)
      if (!user) return

      setSelectedUserInfo(user)
      setShowReferralTree(true)
      setTreeLoading(true)

      const response = await fetch(`/api/admin/network-tree?rootUserId=${userId}&maxDepth=999&lazy=true`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to load referral tree')
      }

      setSelectedUserTree(result.data)
    } catch (error) {
      console.error('Error loading referral tree:', error)
      alert('Failed to load referral tree')
    } finally {
      setTreeLoading(false)
    }
  }

  // Users are already filtered server-side, no need for client-side filtering
  const filteredUsers = users

  const totalPages = Math.ceil(totalUsers / usersPerPage)

  if (loading) {
    return (
      <AdminLayout>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-1/4 mb-6"></div>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="h-10 bg-gray-300 rounded w-1/3"></div>
            </div>
            <div className="divide-y divide-gray-200">
              {Array.from({ length: 10 }).map((_, i) => (
                <div key={i} className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="h-10 w-10 bg-gray-300 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-300 rounded w-1/4 mb-2"></div>
                      <div className="h-3 bg-gray-300 rounded w-1/3"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Users Management</h1>
            <p className="text-gray-600">Manage user accounts and permissions</p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Users className="h-4 w-4" />
            <span>{totalUsers} total users</span>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search users by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                />
                {searching && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-blue"></div>
                  </div>
                )}
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
              >
                <option value="all">All Roles</option>
                <option value="user">Users</option>
                <option value="admin">Admins</option>
              </select>
            </div>
          </div>
        </div>

        {/* Users Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Referral Rank
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Wallet Balance
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Gift System
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Joined
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50 relative">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="relative">
                          <div className="h-10 w-10 rounded-full overflow-hidden">
                            {user.avatar_url ? (
                              <img
                                src={user.avatar_url}
                                alt="Profile picture"
                                className="w-full h-full object-cover object-center"
                              />
                            ) : (
                              <div className="w-full h-full bg-primary-blue/10 flex items-center justify-center">
                                <span className="text-sm font-medium text-primary-blue">
                                  {user.full_name?.charAt(0) || user.email?.charAt(0) || 'U'}
                                </span>
                              </div>
                            )}
                          </div>
                          {/* Referral Rank Badge */}
                          <div className="absolute -bottom-1 -right-1">
                            <ReferralRankBadge
                              userType={user.user_type}
                              size="sm"
                            />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {user.full_name || 'No name'}
                          </div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <Mail className="h-3 w-3 mr-1" />
                            {user.email}
                          </div>
                          {user.phone && (
                            <div className="text-sm text-gray-500 flex items-center">
                              <Phone className="h-3 w-3 mr-1" />
                              {user.phone}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        user.role === 'admin' || user.is_super_admin
                          ? 'bg-purple-100 text-purple-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {user.is_super_admin ? 'Super Admin' : user.role || 'User'}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <ReferralRankBadge
                        userType={user.user_type}
                        showLabel={true}
                        size="sm"
                      />
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        user.banned_until
                          ? 'bg-red-100 text-red-800'
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {user.banned_until ? 'Banned' : 'Active'}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      <div className="flex items-center">
                        <span className="font-medium">
                          Rs {(user.wallet_balance || 0).toLocaleString()}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      <div className="flex items-center">
                        <Award className="h-3 w-3 mr-1 text-pink-500" />
                        <span className="font-medium text-pink-600">
                          Rs {(giftSystemData.get(user.id) || 0).toLocaleString()}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {new Date(user.created_at).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      {!user.is_super_admin && (
                        <UserActions
                          user={user}
                          onRoleUpdate={handleRoleUpdate}
                          onBanUser={handleBanUser}
                          onViewTree={handleViewReferralTree}
                          onViewUserData={handleViewUserData}
                          onUpdatePassword={handleUpdatePassword}
                        />
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {((currentPage - 1) * usersPerPage) + 1} to {Math.min(currentPage * usersPerPage, totalUsers)} of {totalUsers} users
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}

        {/* Referral Tree Modal */}
        {showReferralTree && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg w-full max-w-6xl h-5/6 flex flex-col">
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <Network className="h-6 w-6 text-blue-500" />
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900">Referral Tree</h3>
                    {selectedUserInfo && (
                      <p className="text-sm text-gray-600">
                        {selectedUserInfo.full_name || 'N/A'} ({selectedUserInfo.email})
                      </p>
                    )}
                  </div>
                </div>
                <button
                  onClick={() => {
                    setShowReferralTree(false)
                    setSelectedUserTree(null)
                    setSelectedUserInfo(null)
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="flex-1 overflow-hidden">
                <EnhancedReferralTree
                  rootNode={selectedUserTree}
                  loading={treeLoading}
                  onNodeClick={(user) => {
                    console.log('Node clicked:', user)
                    // You can add more functionality here like showing user details
                  }}
                  className="h-full"
                  onRefresh={() => handleViewReferralTree(selectedUserInfo?.id || '')}
                  isRefreshing={treeLoading}
                />
              </div>

              <div className="p-6 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-600">
                    Click on nodes to view details • Expand/collapse using the arrow buttons
                  </div>
                  <button
                    onClick={() => {
                      setShowReferralTree(false)
                      setSelectedUserTree(null)
                      setSelectedUserInfo(null)
                    }}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* View User Data Modal */}
        {showUserDataModal && selectedUserData && (
          <ViewUserDataModal
            isOpen={showUserDataModal}
            onClose={() => {
              setShowUserDataModal(false)
              setSelectedUserData(null)
            }}
            user={selectedUserData}
          />
        )}

        {/* Update Password Modal */}
        {showPasswordModal && selectedUserForPassword && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Update Password
                </h3>
                <button
                  onClick={() => {
                    setShowPasswordModal(false)
                    setSelectedUserForPassword(null)
                    setNewPassword('')
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">
                  Update password for: <span className="font-medium">{selectedUserForPassword.email}</span>
                </p>
                <p className="text-sm text-gray-600 mb-4">
                  User: <span className="font-medium">{selectedUserForPassword.full_name || 'N/A'}</span>
                </p>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  New Password
                </label>
                <input
                  type="password"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter new password (min 6 characters)"
                  minLength={6}
                />
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowPasswordModal(false)
                    setSelectedUserForPassword(null)
                    setNewPassword('')
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                  disabled={updatingPassword}
                >
                  Cancel
                </button>
                <button
                  onClick={handlePasswordUpdate}
                  disabled={!newPassword || newPassword.length < 6 || updatingPassword}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {updatingPassword ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Updating...
                    </>
                  ) : (
                    <>
                      <Key className="h-4 w-4 mr-2" />
                      Update Password
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
