'use client'

import { useState, useEffect } from 'react'
import {
  ChevronDown,
  ChevronRight,
  Crown,
  Users,
  Network,
  User as UserIcon,
  Shield,
  Plus,
  Minus
} from 'lucide-react'
import { User } from '@/types'

export interface NetworkTreeNode {
  user: User
  children: NetworkTreeNode[]
  level: number
  position: number
  stats?: {
    directReferrals: number
    totalDownline: number
    totalCommission: number
  }
}

interface HierarchicalNetworkTreeProps {
  rootNode: NetworkTreeNode | null
  loading?: boolean
  onNodeClick?: (user: User) => void
  maxDepth?: number
  className?: string
}

interface TreeNodeProps {
  node: NetworkTreeNode
  onNodeClick?: (user: User) => void
  isExpanded: boolean
  onToggleExpand: (nodeId: string) => void
  maxDepth: number
}

function TreeNode({ node, onNodeClick, isExpanded, onToggleExpand, maxDepth }: TreeNodeProps) {
  const [isHovered, setIsHovered] = useState(false)
  const hasChildren = node.children && node.children.length > 0
  const canExpand = hasChildren && (maxDepth >= 999 || node.level < maxDepth)

  const getUserTypeIcon = (userType: string) => {
    switch (userType) {
      case 'okdoi_head':
        return <Crown className="h-5 w-5 text-white" />
      case 'zonal_manager':
        return <Shield className="h-4 w-4 text-white" />
      case 'rsm':
        return <Network className="h-4 w-4 text-white" />
      default:
        return <UserIcon className="h-3 w-3 text-white" />
    }
  }

  const getNodeStyle = (userType: string) => {
    switch (userType) {
      case 'okdoi_head':
        return {
          container: 'bg-gradient-to-br from-green-400 to-green-600 text-white shadow-lg border-2 border-green-300',
          size: 'w-48 h-24',
          textSize: 'text-lg font-bold'
        }
      case 'zonal_manager':
        return {
          container: 'bg-gradient-to-br from-purple-300 to-purple-500 text-white shadow-md border border-purple-200',
          size: 'w-40 h-20',
          textSize: 'text-base font-semibold'
        }
      case 'rsm':
        return {
          container: 'bg-gradient-to-br from-teal-300 to-teal-500 text-white shadow-md border border-teal-200',
          size: 'w-32 h-16',
          textSize: 'text-sm font-medium'
        }
      default:
        return {
          container: 'bg-gradient-to-br from-blue-300 to-blue-500 text-white shadow-sm border border-blue-200',
          size: 'w-28 h-14',
          textSize: 'text-xs font-medium'
        }
    }
  }

  const style = getNodeStyle(node.user.user_type || 'user')

  // For OKDOI Head, show as central node
  if (node.user.user_type === 'okdoi_head') {
    return (
      <div className="flex flex-col items-center space-y-8">
        {/* OKDOI Head Node */}
        <div className="relative">
          <div
            className={`${style.container} ${style.size} rounded-lg flex flex-col items-center justify-center cursor-pointer transform transition-all duration-200 hover:scale-105 ${
              isHovered ? 'shadow-xl' : ''
            }`}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={() => onNodeClick?.(node.user)}
          >
            <div className="flex items-center space-x-2 mb-1">
              {getUserTypeIcon(node.user.user_type || 'user')}
              <span className={style.textSize}>OKDOI</span>
            </div>
            <span className="text-sm opacity-90">(Main Head)</span>
            {hasChildren && (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onToggleExpand(node.user.id)
                }}
                className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 bg-white rounded-full p-1 shadow-md hover:shadow-lg transition-shadow"
              >
                {isExpanded ? (
                  <Minus className="h-4 w-4 text-gray-600" />
                ) : (
                  <Plus className="h-4 w-4 text-gray-600" />
                )}
              </button>
            )}
          </div>
        </div>

        {/* Zonal Managers */}
        {canExpand && isExpanded && hasChildren && (
          <div className="flex flex-wrap justify-center gap-8 max-w-6xl">
            {node.children.map((child, index) => (
              <div key={child.user.id} className="relative">
                {/* Connection line from OKDOI to ZM */}
                <svg
                  className="absolute -top-8 left-1/2 transform -translate-x-1/2"
                  width="2"
                  height="32"
                  viewBox="0 0 2 32"
                >
                  <line
                    x1="1"
                    y1="0"
                    x2="1"
                    y2="32"
                    stroke="#9CA3AF"
                    strokeWidth="2"
                    strokeDasharray="4,4"
                  />
                </svg>
                <TreeNode
                  node={child}
                  onNodeClick={onNodeClick}
                  isExpanded={isExpanded}
                  onToggleExpand={onToggleExpand}
                  maxDepth={maxDepth}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    )
  }

  // For Zonal Managers
  if (node.user.user_type === 'zonal_manager') {
    return (
      <div className="flex flex-col items-center space-y-6">
        {/* Zonal Manager Node */}
        <div className="relative">
          <div
            className={`${style.container} ${style.size} rounded-lg flex flex-col items-center justify-center cursor-pointer transform transition-all duration-200 hover:scale-105 ${
              isHovered ? 'shadow-xl' : ''
            }`}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={() => onNodeClick?.(node.user)}
          >
            <div className="flex items-center space-x-2 mb-1">
              {getUserTypeIcon(node.user.user_type || 'user')}
              <span className={style.textSize}>Zonal Manager-{node.position || (node.level + 1)}</span>
            </div>
            <span className="text-xs opacity-80 truncate max-w-full px-1">{node.user.full_name}</span>
            {hasChildren && (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onToggleExpand(node.user.id)
                }}
                className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 bg-white rounded-full p-1 shadow-md hover:shadow-lg transition-shadow"
              >
                {isExpanded ? (
                  <Minus className="h-3 w-3 text-gray-600" />
                ) : (
                  <Plus className="h-3 w-3 text-gray-600" />
                )}
              </button>
            )}
          </div>
        </div>

        {/* RSMs under this ZM */}
        {canExpand && isExpanded && hasChildren && (
          <div className="flex flex-wrap justify-center gap-4 max-w-4xl">
            {node.children.map((child, index) => (
              <div key={child.user.id} className="relative">
                {/* Connection line from ZM to RSM */}
                <svg
                  className="absolute -top-6 left-1/2 transform -translate-x-1/2"
                  width="2"
                  height="24"
                  viewBox="0 0 2 24"
                >
                  <line
                    x1="1"
                    y1="0"
                    x2="1"
                    y2="24"
                    stroke="#9CA3AF"
                    strokeWidth="1"
                    strokeDasharray="3,3"
                  />
                </svg>
                <TreeNode
                  node={child}
                  onNodeClick={onNodeClick}
                  isExpanded={isExpanded}
                  onToggleExpand={onToggleExpand}
                  maxDepth={maxDepth}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    )
  }

  // For RSMs and regular users
  const style2 = getNodeStyle(node.user.user_type || 'user')
  return (
    <div className="flex flex-col items-center space-y-4">
      {/* RSM/User Node */}
      <div className="relative">
        <div
          className={`${style2.container} ${style2.size} rounded-lg flex flex-col items-center justify-center cursor-pointer transform transition-all duration-200 hover:scale-105 ${
            isHovered ? 'shadow-xl' : ''
          }`}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          onClick={() => onNodeClick?.(node.user)}
        >
          <div className="flex items-center space-x-1 mb-1">
            {getUserTypeIcon(node.user.user_type || 'user')}
            <span className={style2.textSize}>
              {node.user.user_type === 'rsm' ? `RSM ${node.position || (node.level + 1)}` : 'User'}
            </span>
          </div>
          <span className="text-xs opacity-80 truncate max-w-full px-1">{node.user.full_name}</span>
          {hasChildren && (
            <button
              onClick={(e) => {
                e.stopPropagation()
                onToggleExpand(node.user.id)
              }}
              className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 bg-white rounded-full p-0.5 shadow-sm hover:shadow-md transition-shadow"
            >
              {isExpanded ? (
                <Minus className="h-2 w-2 text-gray-600" />
              ) : (
                <Plus className="h-2 w-2 text-gray-600" />
              )}
            </button>
          )}
        </div>
      </div>

      {/* Children under RSM/User */}
      {canExpand && isExpanded && hasChildren && (
        <div className="flex flex-wrap justify-center gap-2 max-w-2xl">
          {node.children.map((child, index) => (
            <div key={child.user.id} className="relative">
              {/* Connection line */}
              <svg
                className="absolute -top-4 left-1/2 transform -translate-x-1/2"
                width="1"
                height="16"
                viewBox="0 0 1 16"
              >
                <line
                  x1="0.5"
                  y1="0"
                  x2="0.5"
                  y2="16"
                  stroke="#9CA3AF"
                  strokeWidth="1"
                  strokeDasharray="2,2"
                />
              </svg>
              <TreeNode
                node={child}
                onNodeClick={onNodeClick}
                isExpanded={isExpanded}
                onToggleExpand={onToggleExpand}
                maxDepth={maxDepth}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default function HierarchicalNetworkTree({
  rootNode,
  loading = false,
  onNodeClick,
  maxDepth = 999,
  className = ''
}: HierarchicalNetworkTreeProps) {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set())

  // Auto-expand root node and first level
  useEffect(() => {
    if (rootNode) {
      const newExpanded = new Set([rootNode.user.id])
      // Auto-expand first level (Zonal Managers)
      rootNode.children.forEach(child => {
        newExpanded.add(child.user.id)
      })
      setExpandedNodes(newExpanded)
    }
  }, [rootNode])

  const toggleExpand = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes)
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId)
    } else {
      newExpanded.add(nodeId)
    }
    setExpandedNodes(newExpanded)
  }

  const expandAll = () => {
    if (!rootNode) return
    const newExpanded = new Set<string>()

    const addAllNodes = (node: NetworkTreeNode) => {
      newExpanded.add(node.user.id)
      node.children.forEach(child => addAllNodes(child))
    }

    addAllNodes(rootNode)
    setExpandedNodes(newExpanded)
  }

  const collapseAll = () => {
    if (!rootNode) return
    setExpandedNodes(new Set([rootNode.user.id]))
  }

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-96 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading network tree...</p>
        </div>
      </div>
    )
  }

  if (!rootNode) {
    return (
      <div className={`flex items-center justify-center h-96 ${className}`}>
        <div className="text-center">
          <Network className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No network data available</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`overflow-auto ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
              <Network className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Hierarchical Network Tree</h2>
              <p className="text-gray-600">OKDOI → Zonal Managers → RSMs → Users</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={expandAll}
              className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm"
            >
              Expand All
            </button>
            <button
              onClick={collapseAll}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm"
            >
              Collapse All
            </button>
            <div className="text-right">
              <div className="text-sm text-gray-600">Total Network Size</div>
              <div className="text-2xl font-bold text-blue-600">
                {rootNode.stats ? rootNode.stats.totalDownline + 1 : 1}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tree Content */}
      <div className="p-8 bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
        <div className="max-w-full flex justify-center">
          <TreeNode
            node={rootNode}
            onNodeClick={onNodeClick}
            isExpanded={expandedNodes.has(rootNode.user.id)}
            onToggleExpand={toggleExpand}
            maxDepth={maxDepth}
          />
        </div>
      </div>

      {/* Legend */}
      <div className="p-6 border-t border-gray-200 bg-white">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Legend</h3>
        <div className="flex flex-wrap gap-6">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-green-400 to-green-600 rounded flex items-center justify-center">
              <Crown className="h-4 w-4 text-white" />
            </div>
            <span className="text-sm text-gray-700">OKDOI Head</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-purple-300 to-purple-500 rounded flex items-center justify-center">
              <Shield className="h-4 w-4 text-white" />
            </div>
            <span className="text-sm text-gray-700">Zonal Manager</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-teal-300 to-teal-500 rounded flex items-center justify-center">
              <Network className="h-4 w-4 text-white" />
            </div>
            <span className="text-sm text-gray-700">Regional Sales Manager (RSM)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-300 to-blue-500 rounded flex items-center justify-center">
              <UserIcon className="h-4 w-4 text-white" />
            </div>
            <span className="text-sm text-gray-700">Regular User</span>
          </div>
        </div>
      </div>
    </div>
  )
}
