import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { supabaseAdmin } from '@/lib/supabase'
import { ReferralSystemService } from '@/lib/services/referralSystem'

export async function GET(request: NextRequest) {
  try {
    // Create Supabase client for auth using SSR
    const cookieStore = cookies()
    const supabaseAuth = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )
    
    // Get the current user
    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin using admin client
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .select('role, is_super_admin')
      .eq('id', user.id)
      .single()

    if (userError || !userData || (userData.role !== 'admin' && !userData.is_super_admin)) {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const rootUserId = searchParams.get('rootUserId')
    const maxDepth = parseInt(searchParams.get('maxDepth') || '999') // Allow unlimited depth by default
    const lazyLoad = searchParams.get('lazy') === 'true'

    let networkTree

    if (rootUserId) {
      // Get network tree for specific user
      networkTree = await getNetworkTreeForUser(rootUserId, maxDepth)
    } else {
      // Get complete network tree starting from OKDOI Head
      const okdoiHead = await ReferralSystemService.getOKDOIHead()
      if (!okdoiHead) {
        return NextResponse.json({
          success: true,
          data: null,
          message: 'No OKDOI Head found'
        })
      }
      
      networkTree = await getNetworkTreeForUser(okdoiHead.id, maxDepth)
    }

    return NextResponse.json({
      success: true,
      data: networkTree
    })

  } catch (error) {
    console.error('Error fetching network tree:', error)
    return NextResponse.json({
      error: 'Failed to fetch network tree',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// Enhanced caching with TTL
const treeCache = new Map<string, { data: any; timestamp: number }>()
const CACHE_TTL = 1 * 60 * 1000 // 1 minute for testing, will increase later

// Clear cache on startup to ensure fresh data
treeCache.clear()

async function getNetworkTreeForUser(userId: string, maxDepth: number = 999) {
  try {
    // Check cache first
    const cacheKey = `${userId}-${maxDepth}`
    const cached = treeCache.get(cacheKey)
    if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
      return cached.data
    }

    // Fetch all data in parallel for better performance
    const [rootUserResult, allUsersResult, allPlacementsResult, allSubscriptionsResult, introducersResult] = await Promise.all([
      // Get root user
      supabaseAdmin
        .from('users')
        .select('*')
        .eq('id', userId)
        .single(),

      // Get all users in the network (with depth limit)
      getAllNetworkUsers(userId, maxDepth),

      // Get all placements in the network
      getAllNetworkPlacements(userId, maxDepth),

      // Get all active subscriptions
      getAllActiveSubscriptions(),

      // Get all introducers (referred_by users) for the network
      getAllIntroducers(userId, maxDepth)
    ])

    if (rootUserResult.error || !rootUserResult.data) {
      throw new Error('Root user not found')
    }

    const rootUser = rootUserResult.data
    const allUsers = allUsersResult
    const allPlacements = allPlacementsResult
    const allSubscriptions = allSubscriptionsResult
    const allIntroducers = introducersResult

    // Build lookup maps for O(1) access
    const userMap = new Map(allUsers.map(user => [user.id, user]))
    const introducerMap = new Map(allIntroducers.map(user => [user.id, user]))
    const placementsByParent = new Map<string, any[]>()
    const subscriptionsByUser = new Map<string, any[]>()

    // Group placements by parent
    allPlacements.forEach(placement => {
      if (!placementsByParent.has(placement.parent_id)) {
        placementsByParent.set(placement.parent_id, [])
      }
      placementsByParent.get(placement.parent_id)!.push(placement)
    })

    // Group subscriptions by user
    allSubscriptions.forEach(sub => {
      if (!subscriptionsByUser.has(sub.user_id)) {
        subscriptionsByUser.set(sub.user_id, [])
      }
      subscriptionsByUser.get(sub.user_id)!.push(sub)
    })

    // Build tree using optimized non-recursive approach
    const buildOptimizedTree = (userId: string, currentDepth: number = 0): any => {
      const user = userMap.get(userId)
      if (!user) return null

      const children: any[] = []

      const placements = placementsByParent.get(userId) || []

      if (maxDepth >= 999 || currentDepth < maxDepth) {
        for (const placement of placements) {
          const childNode = buildOptimizedTree(placement.child_id, currentDepth + 1)
          if (childNode) {
            childNode.position = placement.pos || placement.position || 0
            childNode.placement_type = placement.placement_type
            children.push(childNode)
          }
        }
      } else if (placements.length > 0) {
        // Even if we've reached maxDepth, add placeholder children to show expand button
        for (const placement of placements) {
          const childUser = userMap.get(placement.child_id)
          if (childUser) {
            children.push({
              user: childUser,
              children: [], // Empty children array but node exists
              level: currentDepth + 1,
              position: placement.pos || placement.position || 0,
              placement_type: placement.placement_type,
              hasMoreChildren: true // Flag to indicate there are more children to load
            })
          }
        }
      }

      // Attach subscription data
      user.active_subscription = subscriptionsByUser.get(userId) || []

      // Attach introducer information
      if (user.referred_by_id) {
        const introducer = introducerMap.get(user.referred_by_id)
        user.introducer_name = introducer ? introducer.full_name || introducer.email : 'Unknown'
      } else {
        user.introducer_name = null
      }

      // Calculate stats efficiently (pre-computed) and attach to user object
      const stats = calculateUserStats(userId, allUsers, allPlacements)

      // Attach stats directly to user object for compatibility with UI
      user.direct_referrals_count = stats.directReferrals
      user.total_downline_count = stats.totalDownline
      user.total_commission_earned = stats.totalCommission

      return {
        user,
        children,
        level: currentDepth,
        position: 0,
        stats // Keep stats object for backward compatibility
      }
    }

    const result = buildOptimizedTree(userId, 0)

    // Cache the final result only
    treeCache.set(cacheKey, { data: result, timestamp: Date.now() })

    return result
  } catch (error) {
    console.error('Error building network tree:', error)
    throw error
  }
}

// Optimized helper functions
async function getAllNetworkUsers(rootUserId: string, maxDepth: number) {
  // Use recursive CTE to get all users in the network efficiently
  const { data, error } = await supabaseAdmin.rpc('get_network_users', {
    root_user_id: rootUserId,
    max_depth: maxDepth
  })

  if (error) {
    console.error('Error getting network users with RPC, using fallback:', error)
    // Fallback to recursive query approach
    return await getAllNetworkUsersFallback(rootUserId, maxDepth)
  }

  return data || []
}

async function getAllNetworkUsersFallback(rootUserId: string, maxDepth: number, currentDepth = 0): Promise<any[]> {
  if (currentDepth >= maxDepth && maxDepth < 999) return [] // Only limit if maxDepth is explicitly set to a reasonable number

  // Get root user
  const { data: rootUser } = await supabaseAdmin
    .from('users')
    .select('*')
    .eq('id', rootUserId)
    .single()

  if (!rootUser) return []

  let allUsers = [rootUser]

  // Get children through referral_placements
  const { data: placements } = await supabaseAdmin
    .from('referral_placements')
    .select('child_id')
    .eq('parent_id', rootUserId)

  if (placements && placements.length > 0) {
    for (const placement of placements) {
      const childUsers = await getAllNetworkUsersFallback(placement.child_id, maxDepth, currentDepth + 1)
      allUsers = allUsers.concat(childUsers)
    }
  }

  return allUsers
}

async function getAllNetworkPlacements(rootUserId: string, maxDepth: number) {
  // Get all placements for the network
  const { data, error } = await supabaseAdmin.rpc('get_network_placements', {
    root_user_id: rootUserId,
    max_depth: maxDepth
  })

  if (error) {
    console.error('Error getting network placements with RPC, using fallback:', error)
    // Fallback to recursive query
    return await getAllNetworkPlacementsFallback(rootUserId, maxDepth)
  }

  return data || []
}

async function getAllNetworkPlacementsFallback(rootUserId: string, maxDepth: number, currentDepth = 0): Promise<any[]> {
  if (currentDepth >= maxDepth && maxDepth < 999) return [] // Only limit if maxDepth is explicitly set to a reasonable number

  // Get direct placements
  const { data: placements } = await supabaseAdmin
    .from('referral_placements')
    .select('*')
    .eq('parent_id', rootUserId)

  if (!placements) return []

  let allPlacements = [...placements]

  // Get placements for children recursively
  for (const placement of placements) {
    const childPlacements = await getAllNetworkPlacementsFallback(placement.child_id, maxDepth, currentDepth + 1)
    allPlacements = allPlacements.concat(childPlacements)
  }

  return allPlacements
}

async function getAllActiveSubscriptions() {
  const { data, error } = await supabaseAdmin
    .from('user_subscriptions')
    .select(`
      user_id,
      id,
      status,
      expires_at,
      package:subscription_packages(
        name,
        price,
        currency
      )
    `)
    .eq('status', 'active')
    .gte('expires_at', new Date().toISOString())
    .order('expires_at', { ascending: false })

  return data || []
}

function calculateUserStats(userId: string, allUsers: any[], allPlacements: any[]) {
  // Calculate direct referrals from users table (referred_by_id relationship)
  const directReferrals = allUsers.filter(user => user.referred_by_id === userId).length

  // Calculate total downline using placement data (tree structure)
  const getDownlineCount = (parentId: string, visited = new Set()): number => {
    if (visited.has(parentId)) return 0
    visited.add(parentId)

    const children = allPlacements.filter(p => p.parent_id === parentId)
    let count = children.length

    for (const child of children) {
      count += getDownlineCount(child.child_id, visited)
    }

    return count
  }

  const totalDownline = getDownlineCount(userId)

  // Debug logging to see what's happening
  if (process.env.NODE_ENV === 'development') {
    console.log(`Stats for user ${userId}:`, {
      directReferrals,
      totalDownline,
      totalUsersInNetwork: allUsers.length,
      totalPlacementsInNetwork: allPlacements.length,
      userReferrals: allUsers.filter(user => user.referred_by_id === userId).map(u => u.email),
      userPlacements: allPlacements.filter(p => p.parent_id === userId).map(p => p.child_id)
    })
  }

  return {
    directReferrals,
    totalDownline,
    totalCommission: 0 // Will be calculated separately if needed
  }
}

// Get all introducers (users who referred others in the network)
async function getAllIntroducers(rootUserId: string, maxDepth: number) {
  try {
    // First get all users in the network
    const networkUsers = await getAllNetworkUsers(rootUserId, maxDepth)

    // Get unique referred_by_ids from network users
    const referredByIds = [...new Set(
      networkUsers
        .map(user => user.referred_by_id)
        .filter(id => id !== null && id !== undefined)
    )]

    if (referredByIds.length === 0) {
      return []
    }

    // Fetch introducer details
    const { data: introducers, error } = await supabaseAdmin
      .from('users')
      .select('id, full_name, email')
      .in('id', referredByIds)

    if (error) {
      console.error('Error fetching introducers:', error)
      return []
    }

    return introducers || []
  } catch (error) {
    console.error('Error in getAllIntroducers:', error)
    return []
  }
}
