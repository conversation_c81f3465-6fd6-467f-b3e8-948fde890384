'use client'

import React, { useState, useEffect } from 'react'
import { Network, Users, TrendingUp, Crown, UserPlus, Search, X, Save, Eye } from 'lucide-react'
import AdminLayout from '@/components/admin/AdminLayout'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import HierarchicalNetworkTree, { NetworkTreeNode } from '@/components/admin/HierarchicalNetworkTree'
import dynamic from 'next/dynamic'
import { EnhancedNetworkTreeNode } from '@/components/admin/EnhancedReferralTree'

// Lazy load the heavy referral tree component
const EnhancedReferralTree = dynamic(
  () => import('@/components/admin/EnhancedReferralTree'),
  {
    loading: () => (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
      </div>
    ),
    ssr: false // This component is heavy and not needed for SEO
  }
)
import { User, ZonalManager, RegionalSalesManager } from '@/types'
import { ReferralSystemService } from '@/lib/services/referralSystem'
import { CommissionSystemService } from '@/lib/services/commissionSystem'

export default function ReferralSystemPage() {
  const [stats, setStats] = useState({
    totalUsers: 0,
    zonalManagers: 0,
    regionalManagers: 0,
    totalCommissions: 0,
    pendingCommissions: 0
  })
  const [zonalManagers, setZonalManagers] = useState<ZonalManager[]>([])
  const [regionalManagers, setRegionalManagers] = useState<RegionalSalesManager[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateZM, setShowCreateZM] = useState(false)
  const [showUpgradeRSM, setShowUpgradeRSM] = useState(false)
  const [showAssignOKDOIHead, setShowAssignOKDOIHead] = useState(false)
  const [showNetworkTree, setShowNetworkTree] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [searchUsers, setSearchUsers] = useState<any[]>([])
  const [userSearchTerm, setUserSearchTerm] = useState('')
  const [selectedUser, setSelectedUser] = useState<any>(null)
  const [networkTree, setNetworkTree] = useState<EnhancedNetworkTreeNode | null>(null)
  const [networkTreeLoading, setNetworkTreeLoading] = useState(false)

  // Form states
  const [zmForm, setZmForm] = useState({
    userId: '',
    zoneName: '',
    zoneDescription: '',
    assignedDistricts: [] as string[]
  })
  const [rsmForm, setRsmForm] = useState({
    userId: '',
    zonalManagerId: '',
    regionName: ''
  })
  const [eligibleUsersForRSM, setEligibleUsersForRSM] = useState<User[]>([])
  const [selectedZMForRSM, setSelectedZMForRSM] = useState<string>('')
  const [loadingEligibleUsers, setLoadingEligibleUsers] = useState(false)
  const [userSearchResults, setUserSearchResults] = useState<User[]>([])
  const [okdoiHead, setOkdoiHead] = useState<User | null>(null)

  useEffect(() => {
    loadData()
    checkOKDOIHead()
  }, [])

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchUsersForAssignment(userSearchTerm)
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [userSearchTerm])

  const checkOKDOIHead = async () => {
    try {
      const response = await fetch('/api/admin/create-okdoi-head', {
        method: 'GET',
      })

      if (response.ok) {
        const result = await response.json()
        setOkdoiHead(result.data)
      }
    } catch (error) {
      console.error('Error checking OKDOI Head:', error)
    }
  }

  const createOKDOIHead = async () => {
    try {
      setLoading(true)

      const response = await fetch('/api/admin/create-okdoi-head', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create OKDOI Head')
      }

      setOkdoiHead(result.data)
      setSuccess('OKDOI Head created successfully')
      setTimeout(() => setSuccess(''), 3000)
    } catch (error) {
      console.error('Error creating OKDOI Head:', error)
      setError(error instanceof Error ? error.message : 'Failed to create OKDOI Head')
      setTimeout(() => setError(''), 3000)
    } finally {
      setLoading(false)
    }
  }

  const assignOKDOIHeadToUser = async () => {
    if (!selectedUser) {
      setError('Please select a user')
      return
    }

    try {
      setLoading(true)

      const response = await fetch('/api/admin/create-okdoi-head', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: selectedUser.id
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to assign OKDOI Head')
      }

      setOkdoiHead(result.data)
      setSuccess('OKDOI Head assigned successfully')
      setShowAssignOKDOIHead(false)
      setSelectedUser(null)
      setUserSearchTerm('')
      setSearchUsers([])
      setTimeout(() => setSuccess(''), 3000)
    } catch (error) {
      console.error('Error assigning OKDOI Head:', error)
      setError(error instanceof Error ? error.message : 'Failed to assign OKDOI Head')
      setTimeout(() => setError(''), 3000)
    } finally {
      setLoading(false)
    }
  }

  const searchUsersForAssignment = async (query: string) => {
    if (!query.trim()) {
      setSearchUsers([])
      return
    }

    try {
      const response = await fetch(`/api/admin/search-users?q=${encodeURIComponent(query)}&limit=10`)
      const result = await response.json()

      if (response.ok) {
        setSearchUsers(result.data || [])
      }
    } catch (error) {
      console.error('Error searching users:', error)
    }
  }

  const loadNetworkTree = async (maxDepth: number = 999) => {
    try {
      setNetworkTreeLoading(true)

      // Use unlimited depth to show all users in the tree
      const response = await fetch(`/api/admin/network-tree?maxDepth=${maxDepth}&lazy=true`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to load network tree')
      }

      setNetworkTree(result.data)
    } catch (error) {
      console.error('Error loading network tree:', error)
      setError(error instanceof Error ? error.message : 'Failed to load network tree')
      setTimeout(() => setError(''), 3000)
    } finally {
      setNetworkTreeLoading(false)
    }
  }

  const handleViewNetworkTree = () => {
    setShowNetworkTree(true)
    if (!networkTree) {
      loadNetworkTree()
    }
  }

  const loadData = async () => {
    try {
      setLoading(true)

      // Load referral system stats
      const referralStats = await ReferralSystemService.getReferralSystemStats()

      // Load commission summary
      const commissionSummary = await CommissionSystemService.getCommissionSummary()

      setStats({
        totalUsers: referralStats.totalUsers,
        zonalManagers: referralStats.zonalManagers,
        regionalManagers: referralStats.regionalManagers,
        totalCommissions: commissionSummary.totalCommissionsPaid,
        pendingCommissions: commissionSummary.pendingCommissions
      })

      // Load ZMs and RSMs
      const [zmData, rsmData] = await Promise.all([
        ReferralSystemService.getZonalManagers(),
        ReferralSystemService.getRegionalSalesManagers()
      ])

      setZonalManagers(zmData)
      setRegionalManagers(rsmData)

    } catch (error) {
      console.error('Error loading referral data:', error)
      setError('Failed to load referral system data')
    } finally {
      setLoading(false)
    }
  }

  const handleSearchUsers = async (searchTerm: string) => {
    if (searchTerm.length < 2) {
      setUserSearchResults([])
      return
    }

    try {
      const results = await ReferralSystemService.searchUsers(searchTerm)
      setUserSearchResults(results)
    } catch (error) {
      console.error('Error searching users:', error)
    }
  }

  const handleCreateZM = async () => {
    if (!zmForm.userId || !zmForm.zoneName) {
      setError('Please fill in all required fields')
      return
    }

    try {
      setLoading(true)
      await ReferralSystemService.createZonalManager(
        zmForm.userId,
        zmForm.zoneName,
        zmForm.zoneDescription,
        zmForm.assignedDistricts
      )

      setSuccess('Zonal Manager created successfully')
      setShowCreateZM(false)
      setZmForm({
        userId: '',
        zoneName: '',
        zoneDescription: '',
        assignedDistricts: []
      })
      await loadData()
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to create Zonal Manager')
    } finally {
      setLoading(false)
    }
  }

  const handleZMSelectionForRSM = async (zmId: string) => {
    try {
      setSelectedZMForRSM(zmId)
      setRsmForm({ ...rsmForm, zonalManagerId: zmId, userId: '' })
      setError('') // Clear any previous errors

      if (zmId && typeof zmId === 'string' && zmId.trim() !== '') {
        setLoadingEligibleUsers(true)
        setEligibleUsersForRSM([]) // Clear previous results

        console.log('Loading eligible users for ZM:', zmId)

        // Call the API route to get eligible users (server-side with admin privileges)
        const response = await fetch(`/api/admin/referrals/eligible-users?zmId=${encodeURIComponent(zmId)}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        })

        const result = await response.json()

        console.log('API response:', result)

        if (result.success && Array.isArray(result.data)) {
          // Filter out any invalid users as an extra safety measure
          const validUsers = result.data.filter(user =>
            user &&
            typeof user === 'object' &&
            user.id &&
            typeof user.id === 'string'
          )
          setEligibleUsersForRSM(validUsers)
          console.log(`Set ${validUsers.length} valid eligible users`)
        } else {
          console.warn('Invalid API response:', result)
          setEligibleUsersForRSM([])
          setError(result.error || 'Failed to load eligible users. Please try again.')
        }
      } else {
        setEligibleUsersForRSM([])
        setLoadingEligibleUsers(false)
      }
    } catch (error) {
      console.error('Error in handleZMSelectionForRSM:', error)
      setEligibleUsersForRSM([])
      setError(error instanceof Error ? error.message : 'Failed to load eligible users. Please try again.')
    } finally {
      setLoadingEligibleUsers(false)
    }
  }

  const handleUpgradeRSM = async () => {
    if (!rsmForm.userId) {
      setError('Please select a user to upgrade')
      return
    }

    if (!selectedZMForRSM) {
      setError('Please select a Zonal Manager first')
      return
    }

    try {
      setLoading(true)
      setError('') // Clear any previous errors

      // Call the API route to upgrade user to RSM (server-side with admin privileges)
      const response = await fetch('/api/admin/referrals/upgrade-rsm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: rsmForm.userId,
          zonalManagerId: rsmForm.zonalManagerId || selectedZMForRSM,
          regionName: rsmForm.regionName || undefined,
          upgradedBy: null // Field is nullable, so we pass null instead of invalid UUID
        })
      })

      const result = await response.json()

      if (result.success) {
        setSuccess('User upgraded to RSM successfully')
        setShowUpgradeRSM(false)
        setRsmForm({
          userId: '',
          zonalManagerId: '',
          regionName: ''
        })
        setSelectedZMForRSM('')
        setEligibleUsersForRSM([])
        await loadData()
      } else {
        setError(result.error || 'Failed to upgrade user to RSM')
      }
    } catch (error) {
      console.error('Error upgrading user to RSM:', error)
      setError(error instanceof Error ? error.message : 'Failed to upgrade user to RSM')
    } finally {
      setLoading(false)
    }
  }

  const StatCard = ({ title, value, icon: Icon, color }: {
    title: string
    value: number | string
    icon: any
    color: string
  }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">
            {typeof value === 'number' ? value.toLocaleString() : value}
          </p>
        </div>
      </div>
    </div>
  )

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Network className="h-8 w-8 text-primary-blue" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Referral System</h1>
              <p className="text-gray-600">Manage referral hierarchy and commission distribution</p>
            </div>
          </div>
          <div className="flex space-x-3">
            {okdoiHead && (
              <Button onClick={handleViewNetworkTree} className="flex items-center space-x-2 bg-green-500 hover:bg-green-600">
                <Eye className="h-4 w-4" />
                <span>View Network Tree</span>
              </Button>
            )}
            {!okdoiHead && (
              <>
                <Button onClick={createOKDOIHead} className="flex items-center space-x-2 bg-amber-500 hover:bg-amber-600">
                  <Crown className="h-4 w-4" />
                  <span>Create OKDOI Head</span>
                </Button>
                <Button onClick={() => setShowAssignOKDOIHead(true)} className="flex items-center space-x-2 bg-blue-500 hover:bg-blue-600">
                  <UserPlus className="h-4 w-4" />
                  <span>Assign to Existing User</span>
                </Button>
              </>
            )}
            <Button onClick={() => setShowCreateZM(true)} className="flex items-center space-x-2">
              <UserPlus className="h-4 w-4" />
              <span>Create ZM</span>
            </Button>
            <Button onClick={() => setShowUpgradeRSM(true)} variant="outline" className="flex items-center space-x-2">
              <Crown className="h-4 w-4" />
              <span>Upgrade to RSM</span>
            </Button>
          </div>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <p className="text-green-800">{success}</p>
          </div>
        )}

        {/* OKDOI Head Status */}
        {okdoiHead && (
          <div className="bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Crown className="h-6 w-6 text-amber-600" />
                <div>
                  <h3 className="font-semibold text-amber-900">OKDOI Head Active</h3>
                  <p className="text-amber-700">{okdoiHead.full_name} ({okdoiHead.email})</p>
                  <p className="text-sm text-amber-600">Referral Code: {okdoiHead.referral_code}</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-amber-900">
                  {(okdoiHead as any).totalReferrals || 0}
                </div>
                <div className="text-sm text-amber-600">Total Network Size</div>
              </div>
            </div>
          </div>
        )}

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <StatCard
            title="OKDOI Head"
            value={okdoiHead ? 1 : 0}
            icon={Crown}
            color="bg-amber-500"
          />
          <StatCard
            title="Total Users"
            value={stats.totalUsers}
            icon={Users}
            color="bg-blue-500"
          />
          <StatCard
            title="Zonal Managers"
            value={stats.zonalManagers}
            icon={Network}
            color="bg-purple-500"
          />
          <StatCard
            title="Regional Managers"
            value={stats.regionalManagers}
            icon={Crown}
            color="bg-green-500"
          />
          <StatCard
            title="Total Commissions"
            value={`Rs ${stats.totalCommissions.toLocaleString()}`}
            icon={TrendingUp}
            color="bg-emerald-500"
          />
          <StatCard
            title="Pending Commissions"
            value={`Rs ${stats.pendingCommissions.toLocaleString()}`}
            icon={TrendingUp}
            color="bg-orange-500"
          />
        </div>

        {/* Search */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Search users, ZMs, or RSMs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                />
              </div>
            </div>
            <Button>Search</Button>
          </div>
        </div>

        {/* Zonal Managers */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Zonal Managers</h2>
          </div>
          <div className="p-6">
            {zonalManagers.length === 0 ? (
              <div className="text-center py-8">
                <Network className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No zonal managers found</p>
                <Button onClick={() => setShowCreateZM(true)} className="mt-4">
                  Create First ZM
                </Button>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Zone
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Referral Code
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {zonalManagers.map((zm) => (
                      <tr key={zm.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {zm.user.full_name || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {zm.zone_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                          {zm.user.referral_code || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            zm.is_active
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {zm.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => {/* Handle view details */}}
                            className="text-primary-blue hover:text-primary-blue/80 mr-3"
                          >
                            View
                          </button>
                          {zm.is_active && (
                            <button
                              onClick={() => {/* Handle deactivate */}}
                              className="text-red-600 hover:text-red-800"
                            >
                              Deactivate
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* Regional Sales Managers */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Regional Sales Managers</h2>
          </div>
          <div className="p-6">
            {regionalManagers.length === 0 ? (
              <div className="text-center py-8">
                <Crown className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No regional sales managers found</p>
                <Button onClick={() => setShowUpgradeRSM(true)} className="mt-4">
                  Upgrade First RSM
                </Button>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Region
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Zonal Manager
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Referral Code
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {regionalManagers.map((rsm) => (
                      <tr key={rsm.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {rsm.user.full_name || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {rsm.region_name || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {rsm.zonal_manager?.zone_name || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                          {rsm.user.referral_code || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            rsm.is_active
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {rsm.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => {/* Handle view details */}}
                            className="text-primary-blue hover:text-primary-blue/80 mr-3"
                          >
                            View
                          </button>
                          {rsm.is_active && (
                            <button
                              onClick={() => {/* Handle deactivate */}}
                              className="text-red-600 hover:text-red-800"
                            >
                              Deactivate
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* Create ZM Modal */}
        {showCreateZM && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">Create Zonal Manager</h2>
                <button onClick={() => setShowCreateZM(false)} className="text-gray-400 hover:text-gray-600">
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Search User</label>
                  <Input
                    placeholder="Search by name or email..."
                    value={userSearchTerm}
                    onChange={(e) => {
                      setUserSearchTerm(e.target.value)
                      handleSearchUsers(e.target.value)
                    }}
                    fullWidth
                  />
                  {userSearchResults.length > 0 && (
                    <div className="mt-2 border border-gray-200 rounded-lg max-h-40 overflow-y-auto">
                      {userSearchResults.map((user) => (
                        <button
                          key={user.id}
                          onClick={() => {
                            setZmForm({ ...zmForm, userId: user.id })
                            setUserSearchTerm(user.full_name || user.email)
                            setUserSearchResults([])
                          }}
                          className="w-full text-left px-3 py-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                        >
                          <div className="font-medium">{user.full_name || 'N/A'}</div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                <Input
                  label="Zone Name"
                  value={zmForm.zoneName}
                  onChange={(e) => setZmForm({ ...zmForm, zoneName: e.target.value })}
                  placeholder="Enter zone name"
                  fullWidth
                  required
                />

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Zone Description</label>
                  <textarea
                    value={zmForm.zoneDescription}
                    onChange={(e) => setZmForm({ ...zmForm, zoneDescription: e.target.value })}
                    placeholder="Enter zone description (optional)"
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button variant="outline" onClick={() => setShowCreateZM(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateZM} loading={loading}>
                  <Save className="h-4 w-4 mr-2" />
                  Create ZM
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Upgrade RSM Modal */}
        {showUpgradeRSM && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">Upgrade to RSM</h2>
                <button
                  onClick={() => {
                    setShowUpgradeRSM(false)
                    setSelectedZMForRSM('')
                    setEligibleUsersForRSM([])
                    setRsmForm({ userId: '', zonalManagerId: '', regionName: '' })
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-4">
                {/* Step 1: Select Zonal Manager */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Step 1: Select Zonal Manager <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={selectedZMForRSM}
                    onChange={(e) => handleZMSelectionForRSM(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  >
                    <option value="">Select Zonal Manager</option>
                    {zonalManagers.filter(zm => zm.is_active).map((zm) => (
                      <option key={zm.id} value={zm.id}>
                        {zm.zone_name} - {zm.user.full_name}
                      </option>
                    ))}
                  </select>
                  <p className="text-sm text-gray-500 mt-1">
                    Each ZM can have only 1 RSM for each direct downline. Select a ZM to see eligible users.
                  </p>
                </div>

                {/* Step 2: Select User from ZM's downline */}
                {selectedZMForRSM && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Step 2: Select User from {zonalManagers.find(zm => zm.id === selectedZMForRSM)?.zone_name} <span className="text-red-500">*</span>
                    </label>
                    {loadingEligibleUsers ? (
                      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-blue mr-2"></div>
                          <p className="text-sm text-blue-800">Loading eligible users from the entire network...</p>
                        </div>
                      </div>
                    ) : eligibleUsersForRSM.length > 0 ? (
                      <select
                        value={rsmForm.userId}
                        onChange={(e) => setRsmForm({ ...rsmForm, userId: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                      >
                        <option value="">Select User to Upgrade</option>
                        {eligibleUsersForRSM
                          .filter(user => user && user.id) // Extra safety filter
                          .map((user) => (
                            <option key={user.id} value={user.id}>
                              {(user.full_name || 'N/A')} - {(user.email || 'N/A')}
                            </option>
                          ))}
                      </select>
                    ) : (
                      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <p className="text-sm text-yellow-800">
                          No eligible users found in this ZM's network. Users must not already be RSMs and each direct line can only have 1 RSM.
                        </p>
                      </div>
                    )}
                  </div>
                )}

                <Input
                  label="Region Name (Optional)"
                  value={rsmForm.regionName}
                  onChange={(e) => setRsmForm({ ...rsmForm, regionName: e.target.value })}
                  placeholder="Enter region name"
                  fullWidth
                />
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowUpgradeRSM(false)
                    setSelectedZMForRSM('')
                    setEligibleUsersForRSM([])
                    setRsmForm({ userId: '', zonalManagerId: '', regionName: '' })
                  }}
                >
                  Cancel
                </Button>
                <Button onClick={handleUpgradeRSM} loading={loading}>
                  <Crown className="h-4 w-4 mr-2" />
                  Upgrade to RSM
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Assign OKDOI Head Modal */}
        {showAssignOKDOIHead && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Assign OKDOI Head</h3>
                <button
                  onClick={() => {
                    setShowAssignOKDOIHead(false)
                    setSelectedUser(null)
                    setUserSearchTerm('')
                    setSearchUsers([])
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Search User</label>
                  <div className="relative">
                    <Input
                      value={userSearchTerm}
                      onChange={(e) => setUserSearchTerm(e.target.value)}
                      placeholder="Search by name or email..."
                      fullWidth
                    />
                    {searchUsers.length > 0 && (
                      <div className="absolute z-10 w-full bg-white border border-gray-300 rounded-lg mt-1 max-h-48 overflow-y-auto shadow-lg">
                        {searchUsers.map((user) => (
                          <button
                            key={user.id}
                            onClick={() => {
                              setSelectedUser(user)
                              setUserSearchTerm(user.full_name || user.email)
                              setSearchUsers([])
                            }}
                            className="w-full text-left px-3 py-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                          >
                            <div className="font-medium">{user.full_name || 'N/A'}</div>
                            <div className="text-sm text-gray-500">{user.email}</div>
                            <div className="text-xs text-gray-400">Role: {user.role}</div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {selectedUser && (
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <h4 className="font-medium text-blue-900">Selected User:</h4>
                    <p className="text-blue-800">{selectedUser.full_name || 'N/A'}</p>
                    <p className="text-sm text-blue-600">{selectedUser.email}</p>
                    <p className="text-xs text-blue-500">Current Role: {selectedUser.role}</p>
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowAssignOKDOIHead(false)
                    setSelectedUser(null)
                    setUserSearchTerm('')
                    setSearchUsers([])
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={assignOKDOIHeadToUser}
                  loading={loading}
                  disabled={!selectedUser}
                  className="bg-amber-500 hover:bg-amber-600"
                >
                  <Crown className="h-4 w-4 mr-2" />
                  Assign OKDOI Head
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Network Tree Modal */}
        {showNetworkTree && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg w-full max-w-6xl h-5/6 flex flex-col">
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <Network className="h-6 w-6 text-green-500" />
                  <h3 className="text-xl font-semibold text-gray-900">Complete Network Tree</h3>
                </div>
                <button
                  onClick={() => setShowNetworkTree(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="flex-1 overflow-hidden">
                <EnhancedReferralTree
                  rootNode={networkTree}
                  loading={networkTreeLoading}
                  onNodeClick={(user) => {
                    console.log('Node clicked:', user)
                    // You can add more functionality here like showing user details
                  }}
                  className="h-full"
                  onRefresh={loadNetworkTree}
                  isRefreshing={networkTreeLoading}
                />
              </div>

              <div className="p-6 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-600">
                    Click on nodes to view details • Expand/collapse using the arrow buttons
                  </div>
                  <Button
                    onClick={() => setShowNetworkTree(false)}
                    variant="outline"
                  >
                    Close
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
